package com.xcess.ocs.service;

import com.xcess.ocs.dto.CountryDTO;
import com.xcess.ocs.dto.CountryResponseDTO;
import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.search.CountrySearchDTO;
import com.xcess.ocs.entity.Country;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.CountryMapper;
import com.xcess.ocs.repository.CountryRepository;
import com.xcess.ocs.repository.PrefixRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class CountryService {
    private final CountryRepository countryRepository;

    @Autowired
    private final PrefixRepository prefixRepository;
    private final CountryMapper countryMapper;

//    @PostConstruct
//    public void initializeCountryCodes() {
//        log.info("Initializing country codes");
//        List<Country> countries = countryRepository.findAll();
//        log.debug("Found {} countries to initialize", countries.size());
//
//        for (Country country : countries) {
//            country.setCountryCode(country.getCountryCode().trim());
//            countryRepository.save(country);
//        }
//        log.info("Successfully initialized country codes");
//    }

    /**
     * Get all countries
     */
    public List<CountryDTO> getAllCountries() {
        log.debug("Fetching all countries");
        List<CountryDTO> countries = countryRepository.findAll().stream()
                .map(countryMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} countries", countries.size());
        return countries;
    }

    /**
     * Get paginated list of countries
     */
    public PageResponseDTO<CountryDTO> getCountriesInPage(Pageable pageable) {

        log.debug("Fetching countries in page no: {}", pageable.getPageNumber());
        Page<Country> countryPage = countryRepository.findAll(pageable);
        List<CountryDTO> countries = countryPage.getContent().stream()
                .map(countryMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} countries in page", countries.size());

        return PaginationUtils.buildGetResponseDTO(countries, countryPage);
    }

    /**
     * Search countries by name or code
     */
    public PageResponseDTO<CountryDTO> searchCountries(CountrySearchDTO searchDTO, Pageable pageable) {
        log.debug("Searching countries with term: {}", searchDTO.getSearchTerm());
        
        Page<Country> countryPage = countryRepository.searchCountries(
                searchDTO.getSearchTerm(),
                pageable
        );
        
        List<CountryDTO> countries = countryPage.getContent().stream()
                .map(countryMapper::toDto)
                .collect(Collectors.toList());
                
        log.debug("Found {} countries matching search criteria", countries.size());
        return PaginationUtils.buildGetResponseDTO(countries, countryPage);
    }

    /**
     * Get country by ID
     */
    public CountryDTO getCountryById(Long id) {
        log.debug("Fetching country with ID: {}", id);
        Country country = countryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Country not found with ID: {}", id);
                    return new ResourceNotFoundException("Country not found with ID: " + id);
                });
        return countryMapper.toDto(country);
    }

    /**
     * Create new country with validation
     */
    public CountryResponseDTO createCountry(CountryDTO countryDTO) {
        log.info("Creating new country with name: {}", countryDTO.getName());

        String countryCode = ensureCountryCodeFormat(countryDTO.getCountryCode());
        countryDTO.setCountryCode(countryCode);

        if (countryRepository.existsByNameAndIsDeletedFalse(countryDTO.getName())) {
            log.warn("Attempt to create country with existing name: {}", countryDTO.getName());
            throw new IllegalArgumentException("Country name already exists: " + countryDTO.getName());
        }

        List<Country> existingCountriesWithCode = countryRepository.findByCountryCode(countryCode);
        if (!existingCountriesWithCode.isEmpty()) {
            String existingCountries = existingCountriesWithCode.stream()
                    .map(Country::getName)
                    .collect(Collectors.joining(", "));
            log.warn("Country code {} is already used by countries: {}", countryCode, existingCountries);
            throw new IllegalArgumentException(
                    String.format("Country code %s is already used by countries: %s",
                            countryCode, existingCountries));
        }

        Country country = countryMapper.toEntity(countryDTO);
        country = countryRepository.save(country);
        log.info("Successfully created country with ID: {}", country.getCountryId());

        return CountryResponseDTO.builder()
                .success(true)
                .message("Country created successfully")
                .data(countryMapper.toDto(country))
                .build();
    }

    /**
     * Update existing country with validation
     */
    public CountryResponseDTO updateCountry(Long id, CountryDTO countryDTO) {
        log.info("Updating country with ID: {}", id);
        Country existingCountry = countryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Country not found with ID: {}", id);
                    return new ResourceNotFoundException("Country not found with ID: " + id);
                });

        String countryCode = ensureCountryCodeFormat(countryDTO.getCountryCode());
        countryDTO.setCountryCode(countryCode);

        if (!existingCountry.getName().equalsIgnoreCase(countryDTO.getName()) &&
                countryRepository.existsByNameAndIsDeletedFalse(countryDTO.getName())) {
            log.warn("Country name already exists: {}", countryDTO.getName());
            throw new IllegalArgumentException("Country name already exists: " + countryDTO.getName());
        }

        // Check if the new country code is used by another country
        if (!existingCountry.getCountryCode().equals(countryCode)) {
            List<Country> countriesWithCode = countryRepository.findByCountryCode(countryCode);
            List<Country> otherCountriesWithCode = countriesWithCode.stream()
                    .filter(c -> !c.getCountryId().equals(id))
                    .toList();

            if (!otherCountriesWithCode.isEmpty()) {
                String existingCountries = otherCountriesWithCode.stream()
                        .map(Country::getName)
                        .reduce((a, b) -> a + ", " + b)
                        .orElse("");

                throw new IllegalArgumentException(String.format(
                        "Country code %s is already used by countries: %s",
                        countryCode, existingCountries));
            }
        }

        existingCountry.setName(countryDTO.getName());
        existingCountry.setCountryCode(countryCode);

        existingCountry = countryRepository.save(existingCountry);
        log.info("Successfully updated country with ID: {}", id);

        return CountryResponseDTO.builder()
                .success(true)
                .message("Country updated successfully")
                .data(countryMapper.toDto(existingCountry))
                .build();
    }

    /**
     * Delete country by ID
     */
    public void deleteCountry(Long id) {
        log.info("Deleting country with ID: {}", id);
        if (!countryRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent country with ID: {}", id);
            throw new ResourceNotFoundException("Country not found with ID: " + id);
        }
        // First, check if there is any active prefix using this country
        boolean isReferenced = prefixRepository.existsByCountry_CountryIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete country with ID: {} that is referenced by an active prefix", id);
            throw new ForeignReferenceException("Country cannot be soft deleted because it is referenced by an active prefix.");
        }
        countryRepository.deleteById(id);
        log.info("Successfully deleted country with ID: {}", id);
    }

    /**
     * Ensure country code starts with '+'
     */
    private String ensureCountryCodeFormat(String countryCode) {
        log.debug("Formatting country code: {}", countryCode);
        if (countryCode == null) {
            log.warn("Null country code provided");
            throw new IllegalArgumentException("Country code cannot be null");
        }

        String cleanCode = countryCode.replace("+", "").trim();
        if (!cleanCode.matches("\\d+")) {
            log.warn("Invalid country code format: {}", countryCode);
            throw new IllegalArgumentException("Country code must contain only numbers");
        }

        return cleanCode;
    }
}
