package com.xcess.ocs.service;

import com.xcess.ocs.cache.RatePackageCache;
import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.search.RatePackageSearchDTO;
import com.xcess.ocs.entity.Pulse;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.entity.*;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.exception.ResourceConflictException;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.exception.DuplicateNameException;
import com.xcess.ocs.mapper.RatePackageMapper;
import com.xcess.ocs.repository.PulseRepository;
import com.xcess.ocs.repository.RatePackageAssociationRepository;
import com.xcess.ocs.repository.RatePackageRepository;
import com.xcess.ocs.dto.RatePackageDTO;
import com.xcess.ocs.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Optional;

@Service
@Slf4j
public class RatePackageService {
    private final RatePackageRepository ratePackageRepository;
    private final PulseRepository pulseRepository;
    // private final RatePackageMapper ratePackageMapper;
    private final RatePackageCache ratePackageCache;
    @Autowired
    private RatePackageAssociationRepository ratePackageAssociationRepository;

    @Autowired
    public RatePackageService(RatePackageRepository ratePackageRepository, PulseRepository pulseRepository, RatePackageCache ratePackageCache) {
        this.ratePackageRepository = ratePackageRepository;
        this.pulseRepository = pulseRepository;
        // this.ratePackageMapper = ratePackageMapper;
        this.ratePackageCache = ratePackageCache;
    }

    @Transactional
    public RatePackageDTO createRatePackage(RatePackageDTO ratePackageDTO) {
        log.info("Creating new rate package with name: {}", ratePackageDTO.getPackageName());

        // Check if name already exists
        if (ratePackageRepository.existsByPackageNameAndIsDeletedFalse(ratePackageDTO.getPackageName())) {
            log.warn("Rate package name already exists: {}", ratePackageDTO.getPackageName());
            throw new DuplicateNameException("Rate Package", ratePackageDTO.getPackageName());
        }

        // Find Pulse
        Pulse pulse = pulseRepository.findById(ratePackageDTO.getPulseId())
                .orElseThrow(() -> {
                    log.warn("Pulse not found with ID: {}", ratePackageDTO.getPulseId());
                    return new ResourceNotFoundException("Pulse not found with ID: " + ratePackageDTO.getPulseId());
                });

        ServiceType serviceTypeEnum = ratePackageDTO.getServiceType();
        RatePackageType ratePackageTypeEnum = ratePackageDTO.getRatePackageType();


        // Check if there is already a "master" subtype for the same serviceType and ratePackageType
        if ("master".equalsIgnoreCase(ratePackageDTO.getSubtype())) {
            boolean existsMaster = ratePackageRepository.existsByServiceTypeAndRatePackageTypeAndSubtypeIgnoreCase(
                    serviceTypeEnum,
                    ratePackageTypeEnum,
                    "master"
            );

            if (existsMaster) {
                log.warn("A master rate package already exists for serviceType: {}, ratePackageType: {}",
                        serviceTypeEnum, ratePackageTypeEnum);
                throw new ResourceConflictException("A 'master' rate package already exists for this combination of service type and rate package type.");
            }
        }

        // Determine subtype if applicable
        setSubtypeIfEligible(ratePackageDTO); // Your existing logic for setting subtype (if applicable)

        log.info("Before save, Subtype set to: {}", ratePackageDTO.getSubtype());

        // Convert DTO to Entity and save
        RatePackage ratePackage = RatePackageMapper.toEntity(ratePackageDTO, pulse);
        ratePackage = ratePackageRepository.save(ratePackage);


        log.info("Successfully created rate package with ID: {}, Subtype: {}",
                ratePackage.getRatePackageId(), ratePackage.getSubtype());

        return RatePackageMapper.toDTO(ratePackage);
    }

    // === Private Helper Method ===
    private void setSubtypeIfEligible(RatePackageDTO dto) {
        // Don't override if subtype is already set (from frontend or previous logic)
        if (dto.getSubtype() != null && !dto.getSubtype().isBlank()) {
            return;
        }

        // For non-SELLING type, ensure subtype is not "master"
        if (dto.getType() != Type.SELLING) {
            dto.setSubtype(null);  // Ensure subtype is null if it's not SELLING
            return;
        }

        ServiceType serviceType = dto.getServiceType();
        RatePackageType ratePackageType = dto.getRatePackageType();

        Set<String> validServiceTypes = Set.of("VOICE", "SMS", "USAGE");
        Set<String> validPackageTypes = Set.of("DESTINATION_BASED", "SOURCE_DESTINATION_BASED");

        // Check if it's eligible for "master"
        if (validServiceTypes.contains(serviceType.name()) && validPackageTypes.contains(ratePackageType.name())) {
            boolean existsMaster = ratePackageRepository.existsByServiceTypeAndRatePackageTypeAndSubtypeIgnoreCase(
                    serviceType, ratePackageType, "master"
            );

            // Set subtype to "master" if no master exists, otherwise null
            dto.setSubtype(existsMaster ? null : "master");
        }
    }

    public List<RatePackageDTO> getAllRatePackages() {
        log.debug("Fetching all rate packages");
        List<RatePackageDTO> packages = ratePackageRepository.findAll().stream()
                .map(RatePackageMapper::toDTO)
                .collect(Collectors.toList());
        log.debug("Retrieved {} rate packages", packages.size());
        return packages;
    }

    public PageResponseDTO<RatePackageDTO> getRatePackagesInPages(Pageable pageable) {
        log.debug("Fetching rate packages in pages");
        Page<RatePackage> ratePackages = ratePackageRepository.findAll(pageable);
        List<RatePackageDTO> ratePackageDTOs = ratePackages.getContent().stream()
                .map(RatePackageMapper::toDTO)
                .toList();

        log.debug("Retrieved {} rate packages in a page", ratePackageDTOs.size());
        return PaginationUtils.buildGetResponseDTO(ratePackageDTOs, ratePackages);
    }
//public List<RatePackageDTO> getAllRatePackages() {
//    log.debug("Fetching all rate packages from cache...");
//    List<RatePackageDTO> packages = ratePackageCache.getAllRatePackages();  // ✅ Fetch from cache
//    log.info("Retrieved {} rate packages from cache", packages.size());
//    return packages;
//}
    /**
     * Search prefixes with optional search term and fields
     */
    public PageResponseDTO<RatePackageDTO> searchRatePackages(RatePackageSearchDTO searchDTO, Pageable pageable) {
        log.debug("Searching rate packages with searchTerm: {} and serviceType: {}",
                searchDTO.getSearchTerm(), searchDTO.getServiceType());

        Page<RatePackage> ratePackagesPage = ratePackageRepository.searchRatePackages(
                searchDTO.getSearchTerm(),
                searchDTO.getServiceType(),
                pageable
        );

        List<RatePackageDTO> ratePackages = ratePackagesPage.getContent().stream()
                .map(RatePackageMapper::toDTO)
                .collect(Collectors.toList());

        log.debug("Found {} rate packages matching criteria", ratePackages.size());
        return PaginationUtils.buildGetResponseDTO(ratePackages, ratePackagesPage);
    }

    public RatePackageDTO getPackageById(Long id) {
        log.debug("Fetching rate package with ID: {}", id);
        Optional<RatePackage> ratePackage = ratePackageRepository.findById(id);
        if (!ratePackage.isPresent()) {
            log.warn("Rate package not found with ID: {}", id);
            throw new RuntimeException("Package not found");
        }
        return ratePackage.map(RatePackageMapper::toDTO).get();
    }

    @Transactional
    public RatePackageDTO updateRatePackage(Long id, RatePackageDTO ratePackageDTO) {
        log.info("Updating rate package with ID: {}", id);
        RatePackage existingPackage = ratePackageRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Rate package not found with ID: {}", id);
                    return new ResourceNotFoundException("Rate Package not found with ID: " + id);
                });

        // Check if new name conflicts with other packages
        if (!existingPackage.getPackageName().equalsIgnoreCase(ratePackageDTO.getPackageName()) &&
                ratePackageRepository.existsByPackageNameAndIsDeletedFalse(ratePackageDTO.getPackageName())) {
            log.warn("Rate package name already exists: {}", ratePackageDTO.getPackageName());
            throw new DuplicateNameException("Rate Package", ratePackageDTO.getPackageName());
        }

        // Find pulse
        Pulse pulse = pulseRepository.findById(ratePackageDTO.getPulseId())
                .orElseThrow(() -> {
                    log.warn("Pulse not found with ID: {}", ratePackageDTO.getPulseId());
                    return new ResourceNotFoundException("Pulse not found with ID: " + ratePackageDTO.getPulseId());
                });

        existingPackage.setPackageName(ratePackageDTO.getPackageName());
        existingPackage.setPackageDesc(ratePackageDTO.getPackageDesc());
        existingPackage.setType(ratePackageDTO.getType());
        existingPackage.setServiceType(ratePackageDTO.getServiceType());
        existingPackage.setRatePackageType(ratePackageDTO.getRatePackageType());
        existingPackage.setPulse(pulse);
        existingPackage.setRounding(ratePackageDTO.getRounding());
        existingPackage.setPriceRounding(ratePackageDTO.getPriceRounding());

        RatePackage updatedPackage = ratePackageRepository.save(existingPackage);
        log.info("Successfully updated rate package with ID: {}", id);
        return RatePackageMapper.toDTO(updatedPackage);
    }

    public void deleteRatePackage(Long id) {
        log.info("Deleting rate package with ID: {}", id);
        if (!ratePackageRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent rate package with ID: {}", id);
            throw new ResourceNotFoundException("Rate Package not found with ID: " + id);
        }
        // First, check if there is any active rate package association using this rate package
        boolean isReferenced = ratePackageAssociationRepository.existsByRatePackage_RatePackageIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete rate package with ID: {} that is referenced by an active rate package association", id);
            throw new ForeignReferenceException("Rate Package cannot be soft deleted because it is referenced by an active Association.");
        }
        ratePackageRepository.deleteById(id);
        log.info("Successfully deleted rate package with ID: {}", id);
    }


    public List<RatePackageDTO> getRatePackagesByType(RatePackageType type) {
        log.info("Fetching rate packages of type: {}", type);

        Optional<List<RatePackage>> ratePackages = ratePackageRepository.findByRatePackageType(type);

        if (ratePackages.isEmpty()) {
            log.warn("No rate packages found for type: {}", type);
            throw new ResourceNotFoundException("No Rate Packages found for type: " + type);
        }

        List<RatePackageDTO> packageDTOs = ratePackages.get().stream()
                .map(RatePackageMapper::toDTO)
                .collect(Collectors.toList());
        log.info("Successfully retrieved {} rate packages of type: {}", packageDTOs.size(), type);
        return packageDTOs;
    }

}