package com.xcess.ocs.service;

import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.PartnerDTO;
import com.xcess.ocs.dto.search.PartnerSearchDTO;
import com.xcess.ocs.entity.Partner;
import com.xcess.ocs.exception.DuplicatePartnerException;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.PartnerMapper;
import com.xcess.ocs.repository.AccountRepository;
import com.xcess.ocs.repository.PartnerRepository;
import com.xcess.ocs.util.PaginationUtils;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PartnerService {
    private final PartnerRepository partnerRepository;
    private final AccountRepository accountRepository;
    private final PartnerMapper partnerMapper;

    public PartnerDTO createPartner(@Valid PartnerDTO dto) {
        log.info("Creating new partner with name: {}", dto.getPartnerName());
        validatePartnerType(dto.getPartnerType());
        if (partnerRepository.existsByPartnerNameAndIsDeletedFalse(dto.getPartnerName())) {
            log.warn("Partner name already exists: {}", dto.getPartnerName());
            throw new DuplicatePartnerException("Partner name already exists");
        }
        Partner partner = partnerMapper.toEntity(dto);
        Partner savedPartner = partnerRepository.save(partner);
        log.info("Successfully created partner with ID: {}", savedPartner.getPartnerId());
        return partnerMapper.toDto(savedPartner);
    }

    public List<PartnerDTO> getAllPartners() {
        log.debug("Fetching all partners");
        List<PartnerDTO> partners = partnerRepository.findAll().stream()
                .map(partnerMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} partners", partners.size());
        return partners;
    }

    public PartnerDTO getPartnerById(Long id) {
        log.debug("Fetching partner with ID: {}", id);
        Partner partner = partnerRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Partner not found with ID: {}", id);
                    return new ResourceNotFoundException("Partner not found");
                });
        return partnerMapper.toDto(partner);
    }

    public PartnerDTO updatePartner(Long id, @Valid PartnerDTO dto) {
        log.info("Updating partner with ID: {}", id);
        validatePartnerType(dto.getPartnerType());

        Partner partner = partnerRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Partner not found with ID: {}", id);
                    return new ResourceNotFoundException("Partner not found");
                });

        // Check if new partner name conflicts with other partners
        if (!partner.getPartnerName().equalsIgnoreCase(dto.getPartnerName()) &&
                partnerRepository.existsByPartnerNameAndIsDeletedFalse(dto.getPartnerName())) {
            log.warn("Partner name already exists: {}", dto.getPartnerName());
            throw new DuplicatePartnerException("Partner with name '" + dto.getPartnerName() + "' already exists");
        }

        partner.setPartnerName(dto.getPartnerName());
        partner.setPartnerType(dto.getPartnerType());

        Partner updatedPartner = partnerRepository.save(partner);
        log.info("Successfully updated partner with ID: {}", id);
        return partnerMapper.toDto(updatedPartner);
    }

    public void deletePartner(Long id) {
        log.info("Deleting partner with ID: {}", id);
        if (!partnerRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent partner with ID: {}", id);
            throw new ResourceNotFoundException("Partner not found");
        }
        // First, check if there is any active account using this partner
        boolean isReferenced = accountRepository.existsByPartner_PartnerIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete partner with ID: {} that is referenced by an active account", id);
            throw new ForeignReferenceException("Partner cannot be soft deleted because it is referenced by an active account.");
        }
        partnerRepository.deleteById(id);
        log.info("Successfully deleted partner with ID: {}", id);
    }

    public PageResponseDTO<PartnerDTO> getPartnersInPage(Pageable pageable) {
        log.debug("Fetching partners in pages");
        Page<Partner> partnersPage = partnerRepository.findAll(pageable);
        List<PartnerDTO> partnerDTOs = partnersPage.getContent().stream()
                .map(partnerMapper::toDto)
                .collect(Collectors.toList());
        return PaginationUtils.buildGetResponseDTO(partnerDTOs, partnersPage);
    }

    public PageResponseDTO<PartnerDTO> searchPartners(PartnerSearchDTO partnerSearchDTO, Pageable pageable) {
        log.debug("Searching partners with criteria: {}", partnerSearchDTO);

        String partnerName = partnerSearchDTO != null ? partnerSearchDTO.getPartnerName() : null;
        String partnerType = partnerSearchDTO != null ? partnerSearchDTO.getPartnerType() : null;
        Page<Partner> partnersPage = partnerRepository.searchPartners(
                partnerName,
                partnerType,
                pageable
        );

        List<PartnerDTO> partnerDTOs = partnersPage.getContent().stream()
                .map(partnerMapper::toDto)
                .collect(Collectors.toList());

        log.debug("Found {} partners matching criteria", partnerDTOs.size());
        return PaginationUtils.buildGetResponseDTO(partnerDTOs, partnersPage);
    }

    private void validatePartnerType(String partnerType) {
        if (!"CUSTOMER".equals(partnerType) && !"VENDOR".equals(partnerType) && !"BOTH".equals(partnerType)) {
            throw new IllegalArgumentException("Partner type must be either 'CUSTOMER', 'VENDOR', or 'BOTH'");
        }
    }
}