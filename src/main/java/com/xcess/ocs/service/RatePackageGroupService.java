package com.xcess.ocs.service;

import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.RatePackageAssociationDTO;
import com.xcess.ocs.dto.RatePackageGroupDTO;
import com.xcess.ocs.entity.*;
import com.xcess.ocs.exception.DuplicateNameException;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.RatePackageGroupMapper;
import com.xcess.ocs.repository.ProductPlanAssociationRepository;
import com.xcess.ocs.repository.RatePackageAssociationRepository;
import com.xcess.ocs.repository.RatePackageGroupRepository;
import com.xcess.ocs.repository.RatePackageRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import jakarta.transaction.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RatePackageGroupService {
    private final RatePackageGroupRepository ratePackageGroupRepository;
    private final RatePackageRepository ratePackageRepository;

    @Autowired
    private final ProductPlanAssociationRepository productPlanAssociationRepository;

    @Autowired
    private final RatePackageAssociationRepository ratePackageAssociationRepository;

    @Transactional
    public RatePackageGroupDTO createRatePackageGroup(RatePackageGroupDTO dto) {
        log.info("Creating new rate package group with name: {}", dto.getRatePackageGroupName());

        // Check if name already exists
        if (ratePackageGroupRepository.existsByNameAndIsDeletedFalse(dto.getRatePackageGroupName())) {
            log.warn("Rate package group name already exists: {}", dto.getRatePackageGroupName());
            throw new DuplicateNameException("Rate Package Group", dto.getRatePackageGroupName());
        }

        // Create and save the group
        RatePackageGroup group = RatePackageGroup.builder()
                .name(dto.getRatePackageGroupName())
                .description(dto.getDescription())
                .packageType(RatePackageGroup.PackageType.fromString(dto.getPackageType()))
                .build();

        // Create associations
        log.debug("Adding {} rate packages to group", dto.getRatePackages().size());
        List<RatePackageAssociation> associations = new ArrayList<>();
        for (RatePackageAssociationDTO assocDto : dto.getRatePackages()) {
            RatePackage ratePackage = ratePackageRepository.findById(assocDto.getRatePackage())
                    .orElseThrow(() -> {
                        log.warn("Rate package not found with ID: {}", assocDto.getRatePackage());
                        return new ResourceNotFoundException(
                                "Rate Package not found with ID: " + assocDto.getRatePackage());
                    });

            RatePackageAssociation association = RatePackageAssociation.builder()
                    .ratePackageGroup(group)
                    .ratePackage(ratePackage)
                    .startTime(assocDto.getStartTime())
                    .endTime(assocDto.getEndTime())
                    .build();
            associations.add(association);
        }

        group.setRatePackageAssociations(associations);
        group = ratePackageGroupRepository.save(group);
        log.info("Successfully created rate package group with ID: {}", group.getRatePackageGroupId());
        return RatePackageGroupMapper.toDTO(group);
    }

    public List<RatePackageGroupDTO> getAllRatePackageGroups() {
        log.debug("Fetching all rate package groups");
        List<RatePackageGroupDTO> groups = ratePackageGroupRepository.findAll().stream()
                .map(RatePackageGroupMapper::toDTO)
                .collect(Collectors.toList());
        log.debug("Retrieved {} rate package groups", groups.size());
        return groups;
    }

    public PageResponseDTO<RatePackageGroupDTO> getRatePackageGroupsInPages(Pageable pageable) {
        log.debug("Fetching rate package groups in pages");
        Page<RatePackageGroup> ratePackageGroups = ratePackageGroupRepository.findAll(pageable);
        List<RatePackageGroupDTO> ratePackageGroupDTOs = ratePackageGroups.getContent().stream()
                .map(RatePackageGroupMapper::toDTO)
                .toList();

        log.debug("Retrieved {} rate package groups in a page", ratePackageGroupDTOs.size());
        return PaginationUtils.buildGetResponseDTO(ratePackageGroupDTOs, ratePackageGroups);
    }

    public PageResponseDTO<RatePackageGroupDTO> searchRatePackageGroups(String searchTerm, String packageType, Pageable pageable) {
        log.info("Searching rate package groups with searchTerm: {} and packageType: {}", searchTerm, packageType);

        RatePackageGroup.PackageType type = packageType != null ? RatePackageGroup.PackageType.fromString(packageType) : null;

        Page<RatePackageGroup> ratePackageGroups = ratePackageGroupRepository.searchRatePackageGroups(searchTerm, type, pageable);

        List<RatePackageGroupDTO> groupDTOs = ratePackageGroups.getContent().stream()
                .map(RatePackageGroupMapper::toDTO)
                .toList();

        return PaginationUtils.buildGetResponseDTO(groupDTOs, ratePackageGroups);
    }

    public RatePackageGroupDTO getRatePackageGroupById(Long id) {
        log.debug("Fetching rate package group with ID: {}", id);
        RatePackageGroup group = ratePackageGroupRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Rate package group not found with ID: {}", id);
                    return new ResourceNotFoundException("Rate Package Group not found with ID: " + id);
                });
        return RatePackageGroupMapper.toDTO(group);
    }

    @Transactional
    public RatePackageGroupDTO updateRatePackageGroup(Long id, RatePackageGroupDTO dto) {
        log.info("Updating rate package group with ID: {}", id);
        RatePackageGroup existingGroup = ratePackageGroupRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Rate package group not found with ID: {}", id);
                    return new ResourceNotFoundException("Rate Package Group not found with ID: " + id);
                });

        // Check if new name conflicts with other groups
        if (!existingGroup.getName().equalsIgnoreCase(dto.getRatePackageGroupName()) &&
                ratePackageGroupRepository.existsByNameAndIsDeletedFalse(dto.getRatePackageGroupName())) {
            log.warn("Rate package group name already exists: {}", dto.getRatePackageGroupName());
            throw new DuplicateNameException("Rate Package Group", dto.getRatePackageGroupName());
        }

        // Update basic fields
        existingGroup.setName(dto.getRatePackageGroupName());
        existingGroup.setDescription(dto.getDescription());
        existingGroup.setPackageType(RatePackageGroup.PackageType.fromString(dto.getPackageType()));

        // Clear and replace associations in-place
        List<RatePackageAssociation> existingAssociations = existingGroup.getRatePackageAssociations();
        existingAssociations.clear();

        for (RatePackageAssociationDTO assocDto : dto.getRatePackages()) {
            RatePackage ratePackage = ratePackageRepository.findById(assocDto.getRatePackage())
                    .orElseThrow(() -> {
                        log.warn("Rate package not found with ID: {}", assocDto.getRatePackage());
                        return new ResourceNotFoundException(
                                "Rate Package not found with ID: " + assocDto.getRatePackage());
                    });

            RatePackageAssociation association = RatePackageAssociation.builder()
                    .ratePackageGroup(existingGroup)
                    .ratePackage(ratePackage)
                    .startTime(assocDto.getStartTime())
                    .endTime(assocDto.getEndTime())
                    .build();

            existingAssociations.add(association);
        }

        return RatePackageGroupMapper.toDTO(ratePackageGroupRepository.save(existingGroup));

    }

    @Transactional
    public void deleteRatePackageGroup(Long id) {
        log.info("Deleting rate package group with ID: {}", id);
        if (!ratePackageGroupRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent rate package group with ID: {}", id);
            throw new ResourceNotFoundException("Rate Package Group not found with ID: " + id);
        }
        // First, check if there is any active rate package association using this rate package group
        boolean isReferenced = ratePackageAssociationRepository.existsByRatePackageGroup_RatePackageGroupIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete rate package group with ID: {} that is referenced by an active rate package association", id);
            throw new ForeignReferenceException("Rate Package Group cannot be soft deleted because it is referenced by an active Association.");
        }
        // First, check if there is any active product plan association using this rate package group
         isReferenced = productPlanAssociationRepository.existsByRatePackageGroup_RatePackageGroupIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete rate package group with ID: {} that is referenced by an active product plan association", id);
            throw new ForeignReferenceException("Rate Package Group cannot be soft deleted because it is referenced by an active Association.");
        }
        ratePackageGroupRepository.deleteById(id);
        log.info("Successfully deleted rate package group with ID: {}", id);
    }
}