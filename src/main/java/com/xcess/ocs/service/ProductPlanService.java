package com.xcess.ocs.service;

import com.xcess.ocs.cache.ProductPlanCache;
import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.ProductPlanAssociationDTO;
import com.xcess.ocs.dto.ProductPlanDTO;
import com.xcess.ocs.entity.*;
import com.xcess.ocs.exception.DuplicateNameException;
import com.xcess.ocs.exception.ForeignReferenceException;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.ProductPlanMapper;
import com.xcess.ocs.repository.AccountRepository;
import com.xcess.ocs.repository.ProductPlanAssociationRepository;
import com.xcess.ocs.repository.ProductPlanRepository;
import com.xcess.ocs.repository.RatePackageGroupRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductPlanService {
    private final ProductPlanRepository productPlanRepository;
    private final RatePackageGroupRepository ratePackageGroupRepository;

    @Autowired
    private final AccountRepository accountRepository;

    @Autowired
    private final ProductPlanAssociationRepository productPlanAssociationRepository;

    private final ProductPlanCache productPlanCache;

    @Transactional
    public ProductPlanDTO createProductPlan(ProductPlanDTO dto) {
        log.info("Creating new product plan with name: {}", dto.getName());

        if (productPlanRepository.existsByNameAndIsDeletedFalse(dto.getName())) {
            log.warn("Product plan name already exists: {}", dto.getName());
            throw new DuplicateNameException("Product Plan", dto.getName());
        }

        ProductPlan plan = ProductPlanMapper.toEntity(dto);
        List<ProductPlanAssociation> associations = createAssociations(plan, dto.getRatePackageGroups());
        plan.setRatePackageGroups(associations);

        plan = productPlanRepository.save(plan);
        log.info("Successfully created product plan with ID: {}", plan.getProductPlanId());
        return ProductPlanMapper.toDTO(plan);
    }

    private List<ProductPlanAssociation> createAssociations(ProductPlan plan,
            List<ProductPlanAssociationDTO> associationDTOs) {
        log.debug("Creating {} rate package group associations", associationDTOs.size());
        return associationDTOs.stream()
                .map(assocDto -> {
                    RatePackageGroup group = ratePackageGroupRepository.findById(assocDto.getRatePackageGroupId())
                            .orElseThrow(() -> {
                                log.warn("Rate package group not found with ID: {}",
                                        assocDto.getRatePackageGroupId());
                                return new ResourceNotFoundException(
                                        "Rate Package Group not found with ID: " + assocDto.getRatePackageGroupId());
                            });
                    return ProductPlanAssociation.builder()
                            .productPlan(plan)
                            .ratePackageGroup(group)
                            .startTime(assocDto.getStartTime())
                            .endTime(assocDto.getEndTime())
                            .build();
                })
                .collect(Collectors.toList());
    }

    public List<ProductPlanDTO> getAllProductPlans() {
        log.debug("Fetching all product plans");
        List<ProductPlanDTO> plans = productPlanRepository.findAll().stream()
                .map(ProductPlanMapper::toDTO)
                .collect(Collectors.toList());
        log.debug("Retrieved {} product plans", plans.size());
        return plans;
    }

    public PageResponseDTO<ProductPlanDTO> getProductPlansInPages(Pageable pageable) {
        log.debug("Fetching product plans in pages");
        Page<ProductPlan> productPlans = productPlanRepository.findAll(pageable);
        List<ProductPlanDTO> productPlanDTOs = productPlans.getContent().stream()
                .map(ProductPlanMapper::toDTO)
                .toList();

        log.debug("Retrieved {} product plans in a page", productPlanDTOs.size());
        return PaginationUtils.buildGetResponseDTO(productPlanDTOs, productPlans);
    }
//public List<ProductPlanDTO> getAllProductPlans() {
//    log.debug("Fetching all product plans from cache");
//    return productPlanCache.getAllProductPlans(); // Retrieves from memory instead of DB
//}

    public PageResponseDTO<ProductPlanDTO> searchProductPlans(String searchTerm, String packageType, Pageable pageable) {
        log.info("Searching product plans with searchTerm: {} and packageType: {}", searchTerm, packageType);

        ProductPlan.PackageType type = packageType != null ? ProductPlan.PackageType.fromString(packageType) : null;

        Page<ProductPlan> productPlans = productPlanRepository.searchProductPlans(searchTerm, type, pageable);

        List<ProductPlanDTO> planDTOs = productPlans.getContent().stream()
                .map(ProductPlanMapper::toDTO)
                .toList();

        return PaginationUtils.buildGetResponseDTO(planDTOs, productPlans);
    }

    public ProductPlanDTO getProductPlanById(Long id) {
        log.debug("Fetching product plan with ID: {}", id);
        ProductPlan plan = productPlanRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Product plan not found with ID: {}", id);
                    return new ResourceNotFoundException("Product Plan not found with ID: " + id);
                });
        return ProductPlanMapper.toDTO(plan);
    }

    @Transactional
    public ProductPlanDTO updateProductPlan(Long id, ProductPlanDTO dto) {
        log.info("Updating product plan with ID: {}", id);
        ProductPlan existingPlan = productPlanRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Product plan not found with ID: {}", id);
                    return new ResourceNotFoundException("Product Plan not found with ID: " + id);
                });

        if (!existingPlan.getName().equalsIgnoreCase(dto.getName()) &&
                productPlanRepository.existsByNameAndIsDeletedFalse(dto.getName())) {
            log.warn("Product plan name already exists: {}", dto.getName());
            throw new DuplicateNameException("Product Plan", dto.getName());
        }

        existingPlan.setName(dto.getName());
        existingPlan.setDescription(dto.getDescription());
        existingPlan.setPackageType(ProductPlan.PackageType.fromString(dto.getPackageType()));

        log.debug("Updating rate package group associations");
//        existingPlan.getRatePackageGroups().clear();
//        List<ProductPlanAssociation> associations = createAssociations(existingPlan, dto.getRatePackageGroups());
//        existingPlan.setRatePackageGroups(associations);
        // Clear the existing collection in-place
        List<ProductPlanAssociation> existingAssociations = existingPlan.getRatePackageGroups();
        existingAssociations.clear();

        // Create the new associations and add them to the same collection instance
        List<ProductPlanAssociation> newAssociations = createAssociations(existingPlan, dto.getRatePackageGroups());
        existingAssociations.addAll(newAssociations);

        existingPlan = productPlanRepository.save(existingPlan);
        log.info("Successfully updated product plan with ID: {}", id);
        return ProductPlanMapper.toDTO(existingPlan);
    }

    @Transactional
    public void deleteProductPlan(Long id) {
        log.info("Deleting product plan with ID: {}", id);
        if (!productPlanRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent product plan with ID: {}", id);
            throw new ResourceNotFoundException("Product Plan not found with ID: " + id);
        }
        // First, check if there is any active account using this product plan
        boolean isReferenced = accountRepository.existsByProductPlan_ProductPlanIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete product plan with ID: {} that is referenced by an active account", id);
            throw new ForeignReferenceException("Product Plan cannot be soft deleted because it is referenced by an active account.");
        }
        // First, check if there is any active product plan association using this product plan
        isReferenced = productPlanAssociationRepository.existsByProductPlan_ProductPlanIdAndIsDeletedFalse(id);
        if (isReferenced) {
            log.warn("Attempt to soft delete product plan with ID: {} that is referenced by an active product plan association", id);
            throw new ForeignReferenceException("Product Plan cannot be soft deleted because it is referenced by an active Association.");
        }
        productPlanRepository.deleteById(id);
        log.info("Successfully deleted product plan with ID: {}", id);
    }
}
