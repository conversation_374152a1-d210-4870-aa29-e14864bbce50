package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.RatedCdr;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.service.RateLookupService;
import com.xcess.ocs.ratingengine.service.TrieInitializationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;

/**
 * Service responsible for integrating CDR processing with the rating engine.
 * This service acts as a bridge between Kafka CDR data and the RadixTrie-based
 * rating algorithm, applying rates before database storage.
 * 
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CdrRatingIntegrationService {
    
    private final RateLookupService rateLookupService;
    private final TrieInitializationService trieInitializationService;
    
    // Common timestamp formats to try when parsing CDR timestamps
    private static final DateTimeFormatter[] TIMESTAMP_FORMATTERS = {
        DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),  // Your format: 14-05-2025 02:23:34
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };
    
    /**
     * Process and rate a CDR from Kafka before database storage.
     * This method transforms the flow from "Kafka → Direct Storage" to 
     * "Kafka → Rate Lookup → Rate Application → Enhanced Storage"
     * 
     * @param cdrDto The CDR data from Kafka
     * @param sourceId The source ID for this CDR
     * @return Enhanced RatedCdr entity with rating information applied
     */
    public RatedCdr processAndRateCdr(RatedCdrDTO cdrDto, Long sourceId) {
        log.debug("Processing CDR for rating: calling={}, called={}, sourceId={}", 
                cdrDto.getCallingNumber(), cdrDto.getCalledNumber(), sourceId);
        
        // Create RatedCdr entity from DTO
        RatedCdr ratedCdr = createRatedCdrFromDto(cdrDto, sourceId);
        
        // Check if rating engine is ready
        if (!trieInitializationService.isEngineReady()) {
            log.warn("Rating engine not initialized, marking CDR as failed: calling={}, called={}",
                    cdrDto.getCallingNumber(), cdrDto.getCalledNumber());
            ratedCdr.markAsFailed("Rating engine not initialized");
            return ratedCdr;
        }
        
        // Apply rating using the existing RadixTrie algorithm
        applyRating(ratedCdr);
        
        return ratedCdr;
    }
    
    /**
     * Create a RatedCdr entity from the Kafka DTO
     */
    private RatedCdr createRatedCdrFromDto(RatedCdrDTO cdrDto, Long sourceId) {
        RatedCdr ratedCdr = new RatedCdr();
        
        // Set basic CDR fields
        ratedCdr.setCallingNumber(cdrDto.getCallingNumber());
        ratedCdr.setCalledNumber(cdrDto.getCalledNumber());
        ratedCdr.setStartTime(cdrDto.getStartTime());
        ratedCdr.setEndTime(cdrDto.getEndTime());
        ratedCdr.setIncomingAccountId(cdrDto.getIncomingAccountId());
        ratedCdr.setOutgoingAccountId(cdrDto.getOutgoingAccountId());
        ratedCdr.setSourceId(sourceId);
        
        // Calculate and set call duration
        calculateAndSetDuration(ratedCdr);
        
        // Set initial rating status
        ratedCdr.markAsPending();
        
        return ratedCdr;
    }
    
    /**
     * Apply rating to the CDR using the existing RadixTrie-based algorithm
     */
    private void applyRating(RatedCdr ratedCdr) {
        try {
            log.debug("Applying rating to CDR: calling={}, called={}, startTime='{}', endTime='{}'",
                    ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(),
                    ratedCdr.getStartTime(), ratedCdr.getEndTime());

            // Parse call timestamp for rating validation
            LocalDateTime callTimestamp = parseTimestamp(ratedCdr.getStartTime());
            if (callTimestamp == null) {
                String errorMsg = "Unable to parse call start time: '" + ratedCdr.getStartTime() + "'";
                log.error("Timestamp parsing failed for CDR: calling={}, called={}, startTime='{}'",
                        ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(), ratedCdr.getStartTime());
                ratedCdr.markAsFailed(errorMsg);
                return;
            }
            
            // Use the existing RateLookupService to find the best rate
            // This leverages the two-phase RadixTrie algorithm:
            // Phase 1: Source-Destination matching
            // Phase 2: Destination-only matching (fallback)
            RateDetails bestRate = rateLookupService.findBestRate(
                    ratedCdr.getCallingNumber(),    // Source number
                    ratedCdr.getCalledNumber(),     // Destination number
                    callTimestamp                   // Call timestamp
            );
            
            if (bestRate != null) {
                // Successfully found a matching rate
                markCdrAsRated(ratedCdr, bestRate);
                
                log.info("Successfully rated CDR: calling={}, called={}, rate={}, package={}", 
                        ratedCdr.getCallingNumber(), 
                        ratedCdr.getCalledNumber(),
                        bestRate.getRate(),
                        bestRate.getRatePackage().getPackageName());
                        
            } else {
                // No matching rate found
                String reason = String.format("No matching rate found for source: %s, destination: %s at time: %s",
                        ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(), callTimestamp);
                ratedCdr.markAsUnrated(reason);
                
                log.warn("No rate found for CDR: calling={}, called={}, duration={} minutes",
                        ratedCdr.getCallingNumber(),
                        ratedCdr.getCalledNumber(),
                        ratedCdr.getDurationMinutes());
            }
            
        } catch (Exception e) {
            // Handle any errors during rating
            String reason = "Rating failed due to error: " + e.getMessage();
            ratedCdr.markAsFailed(reason);
            
            log.error("Failed to rate CDR: calling={}, called={}, error={}", 
                    ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(), e.getMessage(), e);
        }
    }
    
    /**
     * Mark the CDR as successfully rated with all relevant information
     */
    private void markCdrAsRated(RatedCdr ratedCdr, RateDetails rateDetails) {
        // Determine if this was a source-destination match or destination-only match
        // This is determined by whether the rate has a source prefix
        boolean isSourceDestMatch = rateDetails.getSourcePrefix() != null && 
                                   !rateDetails.getSourcePrefix().trim().isEmpty();
        
        // Mark the CDR as rated with all the details
        ratedCdr.markAsRated(
                BigDecimal.valueOf(rateDetails.getRate()),           // Applied rate
                rateDetails.getRatePackage().getRatePackageId(),     // Rate package ID
                rateDetails.getRatePackage().getPackageName(),       // Rate package name
                rateDetails.getRateDetailsId(),                      // Rate detail ID
                rateDetails.getSourcePrefix(),                       // Matched source prefix
                rateDetails.getDestinationPrefix(),                  // Matched destination prefix
                isSourceDestMatch                                    // Phase 1 vs Phase 2 match
        );
    }
    
    /**
     * Calculate call duration and set it in the CDR
     */
    private void calculateAndSetDuration(RatedCdr ratedCdr) {
        try {
            LocalDateTime startTime = parseTimestamp(ratedCdr.getStartTime());
            LocalDateTime endTime = parseTimestamp(ratedCdr.getEndTime());
            
            if (startTime != null && endTime != null) {
                long durationSeconds = ChronoUnit.SECONDS.between(startTime, endTime);
                BigDecimal durationMinutes = BigDecimal.valueOf(durationSeconds).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
                ratedCdr.setDurationMinutes(durationMinutes);
                
                log.debug("Calculated call duration: {} minutes for CDR: calling={}, called={}", 
                        durationMinutes, ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber());
            } else {
                log.warn("Unable to calculate duration for CDR: calling={}, called={}, startTime={}, endTime={}", 
                        ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(), 
                        ratedCdr.getStartTime(), ratedCdr.getEndTime());
            }
        } catch (Exception e) {
            log.error("Error calculating duration for CDR: calling={}, called={}, error={}", 
                    ratedCdr.getCallingNumber(), ratedCdr.getCalledNumber(), e.getMessage());
        }
    }
    
    /**
     * Parse timestamp string using multiple common formats
     */
    private LocalDateTime parseTimestamp(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null;
        }
        
        String cleanTimestamp = timestampStr.trim();
        
        for (DateTimeFormatter formatter : TIMESTAMP_FORMATTERS) {
            try {
                return LocalDateTime.parse(cleanTimestamp, formatter);
            } catch (DateTimeParseException e) {
                // Try next formatter
            }
        }
        
        log.warn("Unable to parse timestamp: {}", timestampStr);
        return null;
    }
    
    /**
     * Get detailed rating information for debugging purposes
     */
    public String getDetailedRatingInfo(String callingNumber, String calledNumber, String startTime) {
        LocalDateTime callTimestamp = parseTimestamp(startTime);
        if (callTimestamp == null) {
            return "Unable to parse timestamp: " + startTime;
        }
        
        return rateLookupService.getDetailedLookupInfo(callingNumber, calledNumber, callTimestamp);
    }
}
