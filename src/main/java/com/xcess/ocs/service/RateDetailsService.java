package com.xcess.ocs.service;

import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.RateDetailDTO;
import com.xcess.ocs.dto.RateDetailUploadResponse;
import com.xcess.ocs.entity.Country;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RateDetailsHistory;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.RateDetailMapper;
import com.xcess.ocs.repository.CountryRepository;
import com.xcess.ocs.repository.RateDetailsHistoryRepository;
import com.xcess.ocs.repository.RateDetailsRepository;
import com.xcess.ocs.repository.RatePackageRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Service for managing rate details
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class RateDetailsService {
    // Repositories
    private final RateDetailsRepository rateDetailsRepository;
    private final RatePackageRepository ratePackageRepository;
    private final CountryRepository countryRepository;

    @Autowired
    private RateDetailsHistoryRepository rateDetailsHistoryRepository;

    // Mappers
    private final RateDetailMapper rateDetailMapper;

    // Constants
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
    };

    /**
     * Create a new rate detail
     *
     * @param ratePackageId the rate package ID
     * @param rateDetailDTOs the rate details to create
     * @return the created rate details
     */
    @Transactional
    public List<RateDetailDTO> createRateDetail(Long ratePackageId, List<RateDetailDTO> rateDetailDTOs) {
        List<RateDetailDTO> createdDetails = new ArrayList<>();

//<<<<<<< HEAD
//        Integer currentVersion = rateDetailsRepository.findMaxCurrentVersion();
//        currentVersion = (currentVersion == null) ? 1 : currentVersion + 1;
//
//        List<RateDetails> existingDetails = rateDetailsRepository.findAll();
//=======
        Integer currentVersion = rateDetailsRepository.findMaxCurrentVersionByRatePackageId(ratePackageId);
        int newVersion = (currentVersion == null) ? 1 : currentVersion + 1;


        List<RateDetails> existingDetails = rateDetailsRepository.findByRatePackageRatePackageId(ratePackageId);
        for (RateDetails detail : existingDetails) {
            RateDetailsHistory history = RateDetailsHistory.builder()
                    .rateDetailsId(detail.getRateDetailsId())
                    .destinationPrefix(detail.getDestinationPrefix())
                    .destinationPrefixName(detail.getDestinationPrefixName())
                    .sourcePrefix(detail.getSourcePrefix())
                    .sourcePrefixName(detail.getSourcePrefixName())
                    .rate(detail.getRate())
                    .startTime(detail.getStartTime())
                    .endTime(detail.getEndTime())
                    .versionNumber(detail.getCurrentVersion())
                    .ratePackageId(detail.getRatePackage().getRatePackageId())
                    .sourceCountryId(detail.getSourceCountryId())
                    .sourceCountryCode(detail.getSourceCountryCode())
                    .sourceCountryName(detail.getSourceCountryName())
                    .destinationCountryId(detail.getDestinationCountryId())
                    .destinationCountryCode(detail.getDestinationCountryCode())
                    .destinationCountryName(detail.getDestinationCountryName())
                    .destinationCountryId(detail.getDestinationCountryId())
                    .sourceCountryId(detail.getSourceCountryId())
                    .build();
            rateDetailsHistoryRepository.save(history);
        }


        rateDetailsRepository.deleteByRatePackageId(ratePackageId);

        RatePackage ratePackage = findRatePackageById(ratePackageId);

        List<Country> countries = countryRepository.findAll();
        Map<String, Country> countryMap = countries.stream()
                .collect(Collectors.toMap(
                        Country::getCountryCode,
                        country -> country,
                        (existing, duplicate) -> existing
                ));
        List<String> sortedCountryCodes = countries.stream()
                .map(Country::getCountryCode)
                .distinct()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        for (RateDetailDTO rateDetailDTO : rateDetailDTOs) {
            log.info("Creating new rate detail for destination prefix: {}", rateDetailDTO.getDestinationPrefix());

            validateRateDetail(rateDetailDTO);
            validateRateDetailAgainstPackageType(rateDetailDTO, ratePackage);
            checkDuplicatePrefix(rateDetailDTO, ratePackage);


            // Match source prefix to country and validate
            if (rateDetailDTO.getSourcePrefix() != null) {
                String matchedCode = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getSourcePrefix()::startsWith)
                        .findFirst()
                        .orElse(null);
                if (matchedCode == null) {
                    throw new IllegalArgumentException("Source prefix does not match any country code: " + rateDetailDTO.getSourcePrefix());
                }
                Country country = countryMap.get(matchedCode);
                rateDetailDTO.setSourceCountryCode(country.getCountryCode());
                rateDetailDTO.setSourceCountryName(country.getName());
                rateDetailDTO.setSourceCountryId(country.getCountryId());
                if (!rateDetailDTO.getSourcePrefix().startsWith(country.getCountryCode())) {
                    throw new IllegalArgumentException("Source prefix '" + rateDetailDTO.getSourcePrefix() + "' does not match country code '" + country.getCountryCode() + "'");
                }
            }

            // Match destination prefix to country and validate
            if (rateDetailDTO.getDestinationPrefix() != null) {
                String matchedCode = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getDestinationPrefix()::startsWith)
                        .findFirst()
                        .orElse(null);
                if (matchedCode == null) {
                    throw new IllegalArgumentException("Destination prefix does not match any country code: " + rateDetailDTO.getDestinationPrefix());
                }
                Country country = countryMap.get(matchedCode);
                rateDetailDTO.setDestinationCountryCode(country.getCountryCode());
                rateDetailDTO.setDestinationCountryName(country.getName());
                rateDetailDTO.setDestinationCountryId(country.getCountryId());
                if (!rateDetailDTO.getDestinationPrefix().startsWith(country.getCountryCode())) {
                    throw new IllegalArgumentException("Destination prefix '" + rateDetailDTO.getDestinationPrefix() + "' does not match country code '" + country.getCountryCode() + "'");
                }
            }

            Country sourceCountry = null;
            if (rateDetailDTO.getSourceCountryId() != null) {
                sourceCountry = countryRepository.findById(rateDetailDTO.getSourceCountryId())
                        .orElseThrow(() -> new ResourceNotFoundException("Source country not found with ID: " + rateDetailDTO.getSourceCountryId()));
            }

            Country destinationCountry = null;
            if (rateDetailDTO.getDestinationCountryId() != null) {
                destinationCountry = countryRepository.findById(rateDetailDTO.getDestinationCountryId())
                        .orElseThrow(() -> new ResourceNotFoundException("Destination country not found with ID: " + rateDetailDTO.getDestinationCountryId()));
            }
            RateDetails rateDetail = RateDetails.builder()
                    .ratePackage(ratePackage)
                    .destinationPrefix(rateDetailDTO.getDestinationPrefix())
                    .destinationPrefixName(rateDetailDTO.getDestinationPrefixName())
                    .sourcePrefix(rateDetailDTO.getSourcePrefix())
                    .sourcePrefixName(rateDetailDTO.getSourcePrefixName())
                    .rate(rateDetailDTO.getRate())
                    .startTime(rateDetailDTO.getStartTime())
                    .endTime(rateDetailDTO.getEndTime())
                    .currentVersion(newVersion)
                    .sourceCountryCode(rateDetailDTO.getSourceCountryCode())
                    .sourceCountryName(rateDetailDTO.getSourceCountryName())
                    .destinationCountryCode(rateDetailDTO.getDestinationCountryCode())
                    .destinationCountryName(rateDetailDTO.getDestinationCountryName())
                    .sourceCountryId(sourceCountry) // Set the Country object
                    .destinationCountryId(destinationCountry)
                    .build();

            rateDetail = rateDetailsRepository.save(rateDetail);

            log.info("Successfully created rate detail with ID: {}", rateDetail.getRateDetailsId());
            createdDetails.add(RateDetailMapper.toDTO(rateDetail));

        }

        return createdDetails;
    }

    /**
     * Get all rate details
     *
     * @return list of all rate details
     */
    public List<RateDetailDTO> getAllRateDetails() {
        log.debug("Fetching all rate details");

        List<Country> countries = countryRepository.findAll();
        Map<String, Country> countryMap = countries.stream()
                .collect(Collectors.toMap(
                        Country::getCountryCode,
                        country -> country,
                        (existing, duplicate) -> existing
                ));

        List<String> sortedCountryCodes = countries.stream()
                .map(Country::getCountryCode)
                .distinct()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        List<RateDetailDTO> rateDetails = rateDetailsRepository.findAll().stream()
                .map(rateDetail -> {
                    RateDetailDTO dto = rateDetailMapper.toDTO(rateDetail);

                    if (dto.getSourcePrefix() != null) {
                        sortedCountryCodes.stream()
                                .filter(dto.getSourcePrefix()::startsWith)
                                .findFirst()
                                .map(countryMap::get)
                                .ifPresent(sourceCountry -> {
                                    dto.setSourceCountryCode(sourceCountry.getCountryCode());
                                    dto.setSourceCountryName(sourceCountry.getName());
                                    dto.setSourceCountryId(sourceCountry.getCountryId());
                                });
                    }

                    if (dto.getDestinationPrefix() != null) {
                        sortedCountryCodes.stream()
                                .filter(dto.getDestinationPrefix()::startsWith)
                                .findFirst()
                                .map(countryMap::get)
                                .ifPresent(destinationCountry -> {
                                    dto.setDestinationCountryCode(destinationCountry.getCountryCode());
                                    dto.setDestinationCountryName(destinationCountry.getName());
                                    dto.setDestinationCountryId(destinationCountry.getCountryId());
                                });
                    }

                    return dto;
                })
                .collect(Collectors.toList());

        log.debug("Retrieved {} rate details", rateDetails.size());
        return rateDetails;
    }
    /**
     * Get rate details in pages
     *
     * @param pageable the pagination information
     * @return paginated response with rate details
     */
    public PageResponseDTO<RateDetailDTO> getRateDetailsInPages(Pageable pageable) {
        log.debug("Fetching rate details in pages");
        Page<RateDetails> rateDetailsPage = rateDetailsRepository.findAll(pageable);
        List<RateDetailDTO> rateDetailDTOs = rateDetailsPage.getContent().stream()
                .map(RateDetailMapper::toDTO)
                .toList();

        log.debug("Retrieved {} rate details in a page", rateDetailDTOs.size());
        return PaginationUtils.buildGetResponseDTO(rateDetailDTOs, rateDetailsPage);
    }

    /**
     * Get a rate detail by ID
     *
     * @param id the rate detail ID
     * @return the rate detail
     * @throws ResourceNotFoundException if the rate detail is not found
     */
    public RateDetailDTO getRateDetailById(Long id) {
        log.debug("Fetching rate detail with ID: {}", id);
        return rateDetailsRepository.findById(id)
                .map(RateDetailMapper::toDTO)
                .orElseThrow(() -> {
                    log.warn("Rate detail not found with ID: {}", id);
                    return new ResourceNotFoundException("Rate detail not found with ID: " + id);
                });
    }

    /**
     * Update a rate detail
     *
     * @param id            the rate detail ID
     * @param rateDetailDTO the updated rate detail data
     * @return the updated rate detail
     * @throws ResourceNotFoundException if the rate detail is not found
     */
    @Transactional
    public RateDetailDTO updateRateDetail(Long id, RateDetailDTO rateDetailDTO) {
        log.info("Updating rate detail with ID: {}", id);

        // Fetch the existing RateDetails record
        RateDetails existingDetail = findRateDetailById(id);

        // Validate the input DTO
        validateRateDetail(rateDetailDTO);

        // Fetch all countries and prepare mappings
        List<Country> countries = countryRepository.findAll();
        Map<String, Country> countryMap = countries.stream()
                .collect(Collectors.toMap(
                        Country::getCountryCode,
                        country -> country,
                        (existing, duplicate) -> existing
                ));
        List<String> sortedCountryCodes = countries.stream()
                .map(Country::getCountryCode)
                .distinct()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        // --- Handle source prefix update independently ---
        if (rateDetailDTO.getSourcePrefix() != null) {
            if (!Objects.equals(existingDetail.getSourcePrefix(), rateDetailDTO.getSourcePrefix())) {
                Optional<String> matchedSource = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getSourcePrefix()::startsWith)
                        .findFirst();

                if (matchedSource.isPresent()) {
                    Country matchedCountry = countryMap.get(matchedSource.get());
                    rateDetailDTO.setSourceCountryCode(matchedCountry.getCountryCode());
                    rateDetailDTO.setSourceCountryName(matchedCountry.getName());
                    rateDetailDTO.setSourceCountryId(matchedCountry.getCountryId());
                } else {
                    log.warn("Source prefix does not match any known country code: {}", rateDetailDTO.getSourcePrefix());
                    throw new IllegalArgumentException("Source prefix does not match any known country code: " + rateDetailDTO.getSourcePrefix());
                }
            }
        }

        // --- Handle destination prefix update independently ---
        if (rateDetailDTO.getDestinationPrefix() != null) {
            if (!Objects.equals(existingDetail.getDestinationPrefix(), rateDetailDTO.getDestinationPrefix())) {
                Optional<String> matchedDest = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getDestinationPrefix()::startsWith)
                        .findFirst();

                if (matchedDest.isPresent()) {
                    Country matchedCountry = countryMap.get(matchedDest.get());
                    rateDetailDTO.setDestinationCountryCode(matchedCountry.getCountryCode());
                    rateDetailDTO.setDestinationCountryName(matchedCountry.getName());
                    rateDetailDTO.setDestinationCountryId(matchedCountry.getCountryId());
                } else {
                    log.warn("Destination prefix does not match any known country code: {}", rateDetailDTO.getDestinationPrefix());
                    throw new IllegalArgumentException("Destination prefix does not match any known country code: " + rateDetailDTO.getDestinationPrefix());
                }
            }
        }

        // --- Handle RatePackage update ---
        if (rateDetailDTO.getRatePackageId() != null) {
            log.debug("Fetching rate package with ID: {}", rateDetailDTO.getRatePackageId());
            RatePackage ratePackage = ratePackageRepository.findById(rateDetailDTO.getRatePackageId())
                    .orElseThrow(() -> new ResourceNotFoundException("Rate package not found with ID: " + rateDetailDTO.getRatePackageId()));
            existingDetail.setRatePackage(ratePackage);
        }

        // Update the fields of the existing record
        updateRateDetailFields(existingDetail, rateDetailDTO);

        // Save the updated record
        rateDetailsRepository.save(existingDetail);

        log.info("Successfully updated rate detail with ID: {}", existingDetail.getRateDetailsId());
        return RateDetailMapper.toDTO(existingDetail);
    }

    /**
     * Retrieve history records for a specific rate detail ID.
     *
     * @param rateDetailsId the ID of the rate detail
     * @return a list of RateDetailsHistory entries
     */
    public List<RateDetailsHistory> getRateHistoryByRateDetailsId(Long rateDetailsId) {
        return rateDetailsHistoryRepository.findByRateDetailsId(rateDetailsId);
    }

    /**
     * Delete a rate detail
     *
     * @param id the rate detail ID
     * @throws ResourceNotFoundException if the rate detail is not found
     */
    public void deleteRateDetail(Long id) {
        log.info("Deleting rate detail with ID: {}", id);

        if (!rateDetailsRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent rate detail with ID: {}", id);
            throw new ResourceNotFoundException("Rate detail not found with ID: " + id);
        }

        rateDetailsRepository.deleteById(id);
        log.info("Successfully deleted rate detail with ID: {}", id);
    }
    /**
     * Process a file containing rate details
     *
     * @param file          the file to process
     * @param ratePackageId the rate package ID
     * @return response with the number of records processed
     * @throws IOException               if there is an error reading the file
     * @throws ResourceNotFoundException if the rate package is not found
     * @throws IllegalArgumentException  if there is an error in the file format or content
     */
    @Transactional
    public RateDetailUploadResponse processFile(MultipartFile file, Long ratePackageId) throws IOException {
        validateFile(file);
        RatePackage ratePackage = findRatePackageById(ratePackageId);

//<<<<<<< HEAD
//        Integer currentVersion = Optional.ofNullable(rateDetailsRepository.findMaxCurrentVersion()).orElse(0) + 1;
//
//        moveExistingDetailsToHistory(ratePackageId);
//
//        List<RateDetailDTO> rateDetails = parseFile(file, ratePackageId);
//        validateRateDetails(rateDetails, ratePackageId);
//
//        Map<String, Country> countryMap = getCountryMap();
//        List<String> sortedCountryCodes = getSortedCountryCodes();
//
//        saveNewRateDetails(rateDetails, ratePackage, currentVersion, countryMap, sortedCountryCodes);
//=======
        // Fetch current max version in RateDetails
        Integer currentVersion = rateDetailsRepository.findMaxCurrentVersionByRatePackageId(ratePackageId);
        currentVersion = (currentVersion == null) ? 1 : currentVersion + 1;

        // Move existing RateDetails records to RateDetailsHistory
        List<RateDetails> existingDetails = rateDetailsRepository.findByRatePackageRatePackageId(ratePackageId);
        for (RateDetails detail : existingDetails) {
            RateDetailsHistory history = RateDetailsHistory.builder()
                    .rateDetailsId(detail.getRateDetailsId())
                    .destinationPrefix(detail.getDestinationPrefix())
                    .destinationPrefixName(detail.getDestinationPrefixName())
                    .sourcePrefix(detail.getSourcePrefix())
                    .sourcePrefixName(detail.getSourcePrefixName())
                    .rate(detail.getRate())
                    .startTime(detail.getStartTime())
                    .endTime(detail.getEndTime())
                    .versionNumber(detail.getCurrentVersion())
                    .ratePackageId(detail.getRatePackage().getRatePackageId())
                    .sourceCountryCode(detail.getSourceCountryCode())
                    .sourceCountryName(detail.getSourceCountryName())
                    .destinationCountryCode(detail.getDestinationCountryCode())
                    .destinationCountryName(detail.getDestinationCountryName())
                    .destinationCountryId(detail.getDestinationCountryId())
                    .sourceCountryId(detail.getSourceCountryId())
                    .build();
            rateDetailsHistoryRepository.save(history);
        }

        // Clear existing records in RateDetails
        rateDetailsRepository.deleteByRatePackageId(ratePackageId);

        // Parse and validate the new file
        List<RateDetailDTO> rateDetails = parseFile(file, ratePackageId);
        validateRateDetails(rateDetails, ratePackageId);

        // Fetch all countries for mapping
        List<Country> countries = countryRepository.findAll();
        Map<String, Country> countryMap = countries.stream()
                .collect(Collectors.toMap(
                        Country::getCountryCode,
                        country -> country,
                        (existing, duplicate) -> existing
                ));
        List<String> sortedCountryCodes = countries.stream()
                .map(Country::getCountryCode)
                .distinct()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        // Insert new RateDetails records with updated currentVersion
        for (RateDetailDTO rateDetailDTO : rateDetails) {
            // Validate rate detail against package type
            validateRateDetailAgainstPackageType(rateDetailDTO, ratePackage);

            // Match source prefix to country and validate
            if (rateDetailDTO.getSourcePrefix() != null) {
                String matchedCode = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getSourcePrefix()::startsWith)
                        .findFirst()
                        .orElse(null);
                if (matchedCode == null) {
                    throw new IllegalArgumentException("Source prefix does not match any country code: " + rateDetailDTO.getSourcePrefix());
                }
                Country country = countryMap.get(matchedCode);
                rateDetailDTO.setSourceCountryCode(country.getCountryCode());
                rateDetailDTO.setSourceCountryName(country.getName());
                rateDetailDTO.setSourceCountryId(country.getCountryId());

            }

            // Match destination prefix to country and validate
            if (rateDetailDTO.getDestinationPrefix() != null) {
                String matchedCode = sortedCountryCodes.stream()
                        .filter(rateDetailDTO.getDestinationPrefix()::startsWith)
                        .findFirst()
                        .orElse(null);
                if (matchedCode == null) {
                    throw new IllegalArgumentException("Destination prefix does not match any country code: " + rateDetailDTO.getDestinationPrefix());
                }
                Country country = countryMap.get(matchedCode);
                rateDetailDTO.setDestinationCountryCode(country.getCountryCode());
                rateDetailDTO.setDestinationCountryName(country.getName());
                rateDetailDTO.setDestinationCountryId(country.getCountryId());

            }

            RateDetails rateDetail = RateDetailMapper.toEntity(rateDetailDTO, ratePackage);
            rateDetail.setCurrentVersion(currentVersion);
            rateDetail.setSourceCountryCode(rateDetailDTO.getSourceCountryCode());
            rateDetail.setSourceCountryName(rateDetailDTO.getSourceCountryName());
            rateDetail.setDestinationCountryCode(rateDetailDTO.getDestinationCountryCode());
            rateDetail.setDestinationCountryName(rateDetailDTO.getDestinationCountryName());
//            rateDetail.setSourceCountryId(countryRepository.findById(rateDetailDTO.getSourceCountryId())
//                    .orElseThrow(() -> new ResourceNotFoundException("Source country not found with ID: " + rateDetailDTO.getSourceCountryId())));
//            rateDetail.setDestinationCountryId(countryRepository.findById(rateDetailDTO.getDestinationCountryId())
//                    .orElseThrow(() -> new ResourceNotFoundException("Destination country not found with ID: " + rateDetailDTO.getDestinationCountryId())));

            rateDetailsRepository.save(rateDetail);
        }

        return new RateDetailUploadResponse(rateDetails.size(), "File processed successfully");
    }

    private void moveExistingDetailsToHistory(Long ratePackageId) {
        List<RateDetails> existingDetails = rateDetailsRepository.findByRatePackageRatePackageId(ratePackageId);
        for (RateDetails detail : existingDetails) {
            RateDetailsHistory history = RateDetailsHistory.builder()
                    .rateDetailsId(detail.getRateDetailsId())
                    .destinationPrefix(detail.getDestinationPrefix())
                    .destinationPrefixName(detail.getDestinationPrefixName())
                    .sourcePrefix(detail.getSourcePrefix())
                    .sourcePrefixName(detail.getSourcePrefixName())
                    .rate(detail.getRate())
                    .startTime(detail.getStartTime())
                    .endTime(detail.getEndTime())
                    .versionNumber(detail.getCurrentVersion())
                    .ratePackageId(detail.getRatePackage().getRatePackageId())
                    .sourceCountryCode(detail.getSourceCountryCode())
                    .sourceCountryName(detail.getSourceCountryName())
                    .destinationCountryCode(detail.getDestinationCountryCode())
                    .destinationCountryName(detail.getDestinationCountryName())
                    .destinationCountryId(detail.getDestinationCountryId())
                    .sourceCountryId(detail.getSourceCountryId())
                    .build();
            rateDetailsHistoryRepository.save(history);
        }
        rateDetailsRepository.deleteAllInBatch();
    }

    private Map<String, Country> getCountryMap() {
        return countryRepository.findAll().stream()
                .collect(Collectors.toMap(
                        Country::getCountryCode,
                        country -> country,
                        (existing, duplicate) -> existing
                ));
    }

    private List<String> getSortedCountryCodes() {
        return countryRepository.findAll().stream()
                .map(Country::getCountryCode)
                .distinct()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());
    }

    private void saveNewRateDetails(List<RateDetailDTO> rateDetails, RatePackage ratePackage, Integer currentVersion,
                                    Map<String, Country> countryMap, List<String> sortedCountryCodes) {
        for (RateDetailDTO rateDetailDTO : rateDetails) {
            enrichRateDetailWithCountryData(rateDetailDTO, countryMap, sortedCountryCodes);

            RateDetails rateDetail = RateDetailMapper.toEntity(rateDetailDTO, ratePackage);
            rateDetail.setCurrentVersion(currentVersion);
            rateDetailsRepository.save(rateDetail);
        }
    }

    private void enrichRateDetailWithCountryData(RateDetailDTO rateDetailDTO, Map<String, Country> countryMap,
                                                 List<String> sortedCountryCodes) {
        if (rateDetailDTO.getSourcePrefix() != null) {
            String matchedCode = sortedCountryCodes.stream()
                    .filter(rateDetailDTO.getSourcePrefix()::startsWith)
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Source prefix does not match any country code: " + rateDetailDTO.getSourcePrefix()));
            Country country = countryMap.get(matchedCode);
            rateDetailDTO.setSourceCountryCode(country.getCountryCode());
            rateDetailDTO.setSourceCountryName(country.getName());
            rateDetailDTO.setSourceCountryId(country.getCountryId());
        }

        if (rateDetailDTO.getDestinationPrefix() != null) {
            String matchedCode = sortedCountryCodes.stream()
                    .filter(rateDetailDTO.getDestinationPrefix()::startsWith)
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Destination prefix does not match any country code: " + rateDetailDTO.getDestinationPrefix()));
            Country country = countryMap.get(matchedCode);
            rateDetailDTO.setDestinationCountryCode(country.getCountryCode());
            rateDetailDTO.setDestinationCountryName(country.getName());
            rateDetailDTO.setDestinationCountryId(country.getCountryId());
        }
    }

    // Private helper methods
    /**
     * Find a rate package by ID
     */
    private RatePackage findRatePackageById(Long ratePackageId) {
        return ratePackageRepository.findById(ratePackageId)
                .orElseThrow(() -> {
                    log.warn("Rate package not found with ID: {}", ratePackageId);
                    return new ResourceNotFoundException("Rate package not found with ID: " + ratePackageId);
                });
    }

    /**
     * Find a rate detail by ID
     */
    private RateDetails findRateDetailById(Long id) {
        return rateDetailsRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Rate detail not found with ID: {}", id);
                    return new ResourceNotFoundException("Rate detail not found with ID: " + id);
                });
    }

    /**
     * Validate rate detail
     */
    private void validateRateDetail(RateDetailDTO dto) {
        // Validate destination prefix
        if (dto.getDestinationPrefix() == null || dto.getDestinationPrefix().trim().isEmpty()) {
            throw new IllegalArgumentException("Destination prefix is required.");
        }

        // Determine rating type and validate prefixes
        String ratingType = dto.getSourcePrefix() != null ? "source-destination" : "destination";
        validatePrefixFormat(dto.getDestinationPrefix(), dto.getSourcePrefix(), ratingType);

        // Validate destination prefix name
        if (dto.getDestinationPrefixName() == null || dto.getDestinationPrefixName().trim().isEmpty()) {
            throw new IllegalArgumentException("Destination prefix name is required.");
        }

        // Validate rate
        if (dto.getRate() == null || dto.getRate() <= 0) {
            throw new IllegalArgumentException("Rate must be greater than 0.");
        }

        // Validate time fields
        validateTimeFields(dto);

        // Commented out unused logic
        // if (dto.getSourcePrefix() != null && dto.getSourcePrefix().trim().isEmpty()) {
        //     throw new IllegalArgumentException("Source prefix cannot be empty if provided.");
        // }
    }

    /**
     * Validate rate detail against the rate package type
     * DESTINATION_BASED: Only destination prefix allowed (source prefix must be null/empty)
     * SOURCE_DESTINATION_BASED: Both source and destination prefixes required
     */
    private void validateRateDetailAgainstPackageType(RateDetailDTO dto, RatePackage ratePackage) {
        RatePackageType packageType = ratePackage.getRatePackageType();
        boolean hasSourcePrefix = dto.getSourcePrefix() != null && !dto.getSourcePrefix().trim().isEmpty();

        switch (packageType) {
            case DESTINATION_BASED:
                if (hasSourcePrefix) {
                    throw new IllegalArgumentException(
                        "Source prefix is not allowed for DESTINATION_BASED rate packages. " +
                        "Only destination prefix should be provided."
                    );
                }
                break;

            case SOURCE_DESTINATION_BASED:
                if (!hasSourcePrefix) {
                    throw new IllegalArgumentException(
                        "Source prefix is required for SOURCE_DESTINATION_BASED rate packages. " +
                        "Both source and destination prefixes must be provided."
                    );
                }
                // Validate source prefix name is provided when source prefix exists
                if (dto.getSourcePrefixName() == null || dto.getSourcePrefixName().trim().isEmpty()) {
                    throw new IllegalArgumentException("Source prefix name is required when source prefix is provided.");
                }
                break;

            default:
                throw new IllegalArgumentException("Unsupported rate package type: " + packageType);
        }

        log.debug("Rate detail validation passed for package type: {}, hasSourcePrefix: {}",
                 packageType, hasSourcePrefix);
    }

    /**
     * Check for duplicate prefix
     */
    private void checkDuplicatePrefix(RateDetailDTO dto, RatePackage ratePackage) {
        if (dto.getSourcePrefix() != null && !dto.getSourcePrefix().trim().isEmpty()) {
            // Check for source-destination-based duplicates
            boolean exists = rateDetailsRepository.existsBySourcePrefixAndDestinationPrefixAndRatePackageRatePackageId(
                    dto.getSourcePrefix(),
                    dto.getDestinationPrefix(),
                    ratePackage.getRatePackageId());
            if (exists) {
                throw new IllegalArgumentException(
                        "Rate detail already exists for source prefix " + dto.getSourcePrefix() +
                                " and destination prefix " + dto.getDestinationPrefix());
            }
        } else {
            // Check for destination-based duplicates
            boolean exists = rateDetailsRepository.existsByDestinationPrefixAndRatePackageRatePackageId(
                    dto.getDestinationPrefix(),
                    ratePackage.getRatePackageId());
            if (exists) {
                throw new IllegalArgumentException(
                        "Rate detail already exists for destination prefix " + dto.getDestinationPrefix());
            }
        }
    }
    /*
     * Check if a prefix is unique within a rate package
     */
//    private void checkPrefixUniqueness(String prefix, Long ratePackageId) {
//        if (rateDetailsRepository.existsByPrefixAndRatePackageRatePackageId(prefix, ratePackageId)) {
//            log.warn("Prefix {} already exists for rate package ID: {}", prefix, ratePackageId);
//            throw new IllegalArgumentException("Prefix " + prefix + " already exists for this rate package");
//        }
//    }


    /**
     * Update rate detail fields
     */
    private void updateRateDetailFields(RateDetails existingDetail, RateDetailDTO rateDetailDTO) {
        if (rateDetailDTO.getSourcePrefix() != null && rateDetailDTO.getDestinationPrefix() != null && rateDetailDTO.getRatePackageId() != null) {
            boolean duplicateExists = rateDetailsRepository.existsBySourcePrefixAndDestinationPrefixAndRatePackageRatePackageId(
                    rateDetailDTO.getSourcePrefix(),
                    rateDetailDTO.getDestinationPrefix(),
                    rateDetailDTO.getRatePackageId()
            );

            if (duplicateExists && !Objects.equals(existingDetail.getRateDetailsId(), rateDetailDTO.getRateDetailsId())) {
                throw new IllegalArgumentException("A rate detail with the same source prefix, destination prefix, and rate package already exists.");
            }
        }
        // Update fields only if they are not null in the DTO
        if (rateDetailDTO.getDestinationPrefix() != null) {
            log.debug("Updating destination prefix: {}", rateDetailDTO.getDestinationPrefix());
            existingDetail.setDestinationPrefix(rateDetailDTO.getDestinationPrefix());
        }
        if (rateDetailDTO.getDestinationPrefixName() != null) {
            log.debug("Updating destination prefix name: {}", rateDetailDTO.getDestinationPrefixName());
            existingDetail.setDestinationPrefixName(rateDetailDTO.getDestinationPrefixName());
        }
        if (rateDetailDTO.getSourceCountryCode() != null) {
            log.debug("Updating source country code: {}", rateDetailDTO.getSourceCountryCode());
            existingDetail.setSourceCountryCode(rateDetailDTO.getSourceCountryCode());
        }
        if (rateDetailDTO.getSourceCountryName() != null) {
            log.debug("Updating source country name: {}", rateDetailDTO.getSourceCountryName());
            existingDetail.setSourceCountryName(rateDetailDTO.getSourceCountryName());
        }
        if (rateDetailDTO.getDestinationCountryCode() != null) {
            log.debug("Updating destination country code: {}", rateDetailDTO.getDestinationCountryCode());
            existingDetail.setDestinationCountryCode(rateDetailDTO.getDestinationCountryCode());
        }
        if (rateDetailDTO.getDestinationCountryName() != null) {
            log.debug("Updating destination country name: {}", rateDetailDTO.getDestinationCountryName());
            existingDetail.setDestinationCountryName(rateDetailDTO.getDestinationCountryName());
        }
        if (rateDetailDTO.getSourcePrefix() != null) {
            log.debug("Updating source prefix: {}", rateDetailDTO.getSourcePrefix());
            existingDetail.setSourcePrefix(rateDetailDTO.getSourcePrefix());
        }
        if (rateDetailDTO.getSourcePrefixName() != null) {
            log.debug("Updating source prefix name: {}", rateDetailDTO.getSourcePrefixName());
            existingDetail.setSourcePrefixName(rateDetailDTO.getSourcePrefixName());
        }
        if (rateDetailDTO.getRate() != null) {
            log.debug("Updating rate: {}", rateDetailDTO.getRate());
            existingDetail.setRate(rateDetailDTO.getRate());
        }
        if (rateDetailDTO.getStartTime() != null) {
            log.debug("Updating start time: {}", rateDetailDTO.getStartTime());
            existingDetail.setStartTime(rateDetailDTO.getStartTime());
        }
        if (rateDetailDTO.getEndTime() != null) {
            log.debug("Updating end time: {}", rateDetailDTO.getEndTime());
            existingDetail.setEndTime(rateDetailDTO.getEndTime());
        }

        // Fetch and set related entities if provided
        if (rateDetailDTO.getSourceCountryId() != null) {
            log.debug("Fetching source country with ID: {}", rateDetailDTO.getSourceCountryId());
            Country sourceCountry = countryRepository.findById(rateDetailDTO.getSourceCountryId())
                    .orElseThrow(() -> new ResourceNotFoundException("Source country not found with ID: " + rateDetailDTO.getSourceCountryId()));
            existingDetail.setSourceCountryId(sourceCountry);
        }
        if (rateDetailDTO.getDestinationCountryId() != null) {
            log.debug("Fetching destination country with ID: {}", rateDetailDTO.getDestinationCountryId());
            Country destinationCountry = countryRepository.findById(rateDetailDTO.getDestinationCountryId())
                    .orElseThrow(() -> new ResourceNotFoundException("Destination country not found with ID: " + rateDetailDTO.getDestinationCountryId()));
            existingDetail.setDestinationCountryId(destinationCountry);
        }

        // Fetch and set RatePackageId only if provided, otherwise retain existing
        if (rateDetailDTO.getRatePackageId() != null) {
            log.debug("Fetching rate package with ID: {}", rateDetailDTO.getRatePackageId());
            RatePackage ratePackage = ratePackageRepository.findById(rateDetailDTO.getRatePackageId())
                    .orElseThrow(() -> new ResourceNotFoundException("Rate package not found with ID: " + rateDetailDTO.getRatePackageId()));
            existingDetail.setRatePackage(ratePackage);
        }
    }
    /**
     * Validate file
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be empty");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("Invalid file name");
        }

        if (!fileName.endsWith(".csv") && !fileName.endsWith(".xlsx")) {
            throw new IllegalArgumentException("Unsupported file format. Please upload CSV or XLSX file.");
        }
    }

    /**
     * Parse file content
     */
    private List<RateDetailDTO> parseFile(MultipartFile file, Long ratePackageId) throws IOException {
        String fileName = file.getOriginalFilename();

        if (fileName.endsWith(".csv")) {
            return processCSVFile(file, ratePackageId);
        } else {
            return processExcelFile(file, ratePackageId);
        }
    }

    private void validateRateDetails(List<RateDetailDTO> rateDetails, Long ratePackageId) {
        Set<String> prefixesInFile = new HashSet<>();
        for (RateDetailDTO dto : rateDetails) {
            String combinedPrefix = dto.getSourcePrefix() != null
                    ? dto.getSourcePrefix() + "-" + dto.getDestinationPrefix()
                    : dto.getDestinationPrefix();
            if (!prefixesInFile.add(combinedPrefix)) {
                throw new IllegalArgumentException("Duplicate prefix found in file: " + combinedPrefix);
            }
        }

        List<String> existingPrefixes = rateDetailsRepository
                .findByRatePackageRatePackageId(ratePackageId)
                .stream()
                .map(rateDetail -> rateDetail.getSourcePrefix() != null
                        ? rateDetail.getSourcePrefix() + "-" + rateDetail.getDestinationPrefix()
                        : rateDetail.getDestinationPrefix())
                .collect(Collectors.toList());

        for (RateDetailDTO dto : rateDetails) {
            String combinedPrefix = dto.getSourcePrefix() != null
                    ? dto.getSourcePrefix() + "-" + dto.getDestinationPrefix()
                    : dto.getDestinationPrefix();
            if (existingPrefixes.contains(combinedPrefix)) {
                throw new IllegalArgumentException(
                        "Prefix " + combinedPrefix + " already exists for this rate package");
            }
        }
//        Set<String> prefixesInFile = new HashSet<>();
//        for (RateDetailDTO dto : rateDetails) {
//            if (!prefixesInFile.add(dto.getPrefix())) {
//                throw new IllegalArgumentException("Duplicate prefix found in file: " + dto.getPrefix());
//            }
//        }

        // Check for existing prefixes in the database
//        List<String> existingPrefixes = rateDetailsRepository
//                .findByRatePackageRatePackageId(ratePackageId)
//                .stream()
//                .map(RateDetails::getPrefix)
//                .collect(Collectors.toList());
//
//        for (RateDetailDTO dto : rateDetails) {
//            if (existingPrefixes.contains(dto.getPrefix())) {
//                throw new IllegalArgumentException(
//                        "Prefix " + dto.getPrefix() + " already exists for this rate package");
//            }
//        }
    }

    private List<RateDetailDTO> processCSVFile(MultipartFile file, Long ratePackageId) throws IOException {
        List<RateDetailDTO> rateDetails = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
             CSVReader csvReader = new CSVReader(reader)) {

            String[] header = csvReader.readNext();
            validateCSVHeader(header);

            String[] line;
            while ((line = csvReader.readNext()) != null) {
                try {
                    if (line.length < 5) {
                        throw new IllegalArgumentException("Invalid number of columns in line: " + String.join(",", line));
                    }

                    RateDetailDTO.RateDetailDTOBuilder builder = RateDetailDTO.builder()
                            .destinationPrefix(line[0])
                            .destinationPrefixName(line[1])
                            .rate(Double.parseDouble(line[2]))
                            .startTime(parseDateTime(line[3]))
                            .endTime(parseDateTime(line[4]))
                            .ratePackageId(ratePackageId);

                    if (line.length > 5) {
                        builder.sourcePrefix(line[5])
                                .sourcePrefixName(line[6]);
                    }

                    RateDetailDTO dto = builder.build();
                    validateRateDetailDTO(dto);
                    rateDetails.add(dto);
                } catch (Exception e) {
                    throw new IllegalArgumentException(
                            "Error in line: " + String.join(",", line) + ". " + e.getMessage());
                }
            }
        } catch (CsvValidationException e) {
            throw new IOException("Error reading CSV file", e);
        }

        return rateDetails;
    }

    /**
     * Process Excel file
     */
    private List<RateDetailDTO> processExcelFile(MultipartFile file, Long ratePackageId) throws IOException {
        List<RateDetailDTO> rateDetails = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            validateExcelHeader(headerRow);

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    try {
                        RateDetailDTO dto = parseExcelRow(row, ratePackageId);
                        validateRateDetailDTO(dto);
                        rateDetails.add(dto);
                    } catch (Exception e) {
                        throw new IllegalArgumentException("Error in row " + (i + 1) + ": " + e.getMessage());
                    }
                }
            }
        }

        return rateDetails;
    }


    /**
     * Save rate details
     */
    @Transactional
    private int saveRateDetails(List<RateDetailDTO> rateDetails, RatePackage ratePackage) {
        int recordsProcessed = 0;
        for (RateDetailDTO dto : rateDetails) {
            try {
                RateDetails entity = RateDetailMapper.toEntity(dto, ratePackage);
                rateDetailsRepository.save(entity);
                recordsProcessed++;
            } catch (DataIntegrityViolationException e) {
                throw new IllegalArgumentException("Error saving rate detail: " + e.getMessage());
            }
        }
        return recordsProcessed;
    }

    /**
     * Validate rate detail DTO
     */
    private void validateRateDetailDTO(RateDetailDTO dto) {
        if (dto.getDestinationPrefix() == null) {
            throw new IllegalArgumentException("Destination prefix is required");
        }

        validatePrefixFormat(dto.getDestinationPrefix(), dto.getSourcePrefix(),"destination");

        if (dto.getDestinationPrefixName() == null || dto.getDestinationPrefixName().trim().isEmpty()) {
            throw new IllegalArgumentException("Destination prefix name is required");
        }
        if (dto.getRate() == null || dto.getRate() <= 0) {
            throw new IllegalArgumentException("Rate must be greater than 0");
        }
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            throw new IllegalArgumentException("Start time and end time are required");
        }
        if (dto.getStartTime().isAfter(dto.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }
    }

    /**
     * Validate prefix format
     */
    private void validatePrefixFormat(String prefix, String prefix2, String type) {
        if (prefix == null || !prefix.matches("^\\d{1,6}(-\\d{1,6})?$")) {
            throw new IllegalArgumentException(
                    "Invalid " + type + " prefix format. It must be a number and may contain a single hyphen (e.g., '91', '1-345').");
        }
        if (prefix2 != null && !prefix2.equals("")  && !prefix2.matches("^\\d{1,6}(-\\d{1,6})?$")) {
            throw new IllegalArgumentException(
                    "Invalid " + type + " prefix2 format. It must be a number and may contain a single hyphen (e.g., '91', '1-345').");
        }
        if (type.equals("source") && prefix2 != null && prefix2.trim().isEmpty()) {
            throw new IllegalArgumentException("Source prefix name cannot be empty when source prefix is provided.");
        }
    }
    /**
     * Validate time fields
     */
    private void validateTimeFields(RateDetailDTO dto) {
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            throw new IllegalArgumentException("Start time and end time are required");
        }
        if (dto.getStartTime().isAfter(dto.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }
    }

    /**
     * Validate Excel header
     */
    private void validateExcelHeader(Row headerRow) {
        if (headerRow == null || headerRow.getPhysicalNumberOfCells() != 7) {
            throw new IllegalArgumentException(
                    "Invalid Excel header format. Expected: destinationPrefix,destinationPrefixName,rate,startTime,endTime");
        }
        // Add similar header validation as CSV if needed
    }

    /**
     * Parse date time string
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            throw new IllegalArgumentException("Date cannot be empty");
        }

        // Try each formatter
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDateTime.parse(dateTimeStr, formatter);
            } catch (DateTimeParseException e) {
                // Try next formatter
            }
        }

        // If we get here, none of the formatters worked
        throw new IllegalArgumentException(
                "Invalid date format: " + dateTimeStr
                        + ". Expected formats: yyyy-MM-dd HH:mm:ss, dd-MM-yyyy HH:mm, etc.");
    }

    /**
     * Parse CSV line
     */
    private RateDetailDTO parseCSVLine(String[] line, Long ratePackageId) {
        try {
            RateDetailDTO.RateDetailDTOBuilder builder = RateDetailDTO.builder()
                    .destinationPrefix(line[0])
                    .destinationPrefixName(line[1])
                    .rate(Double.parseDouble(line[2]))
                    .startTime(parseDateTime(line[3]))
                    .endTime(parseDateTime(line[4]))
                    .ratePackageId(ratePackageId);

            // Add source fields if present
            if (line.length > 6) {
                builder.sourcePrefix(line[5])
                        .sourcePrefixName(line[6])
                        .currentVersion(1);
            }

            return builder.build();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number format in line: " + String.join(",", line));
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Error in line: " + String.join(",", line) + ". " + e.getMessage());
        } catch (ArrayIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid number of columns in line: " + String.join(",", line));
        }
    }

    /**
     * Parse Excel row
     */
    private RateDetailDTO parseExcelRow(Row row, Long ratePackageId) {
        try {
            String destinationPrefix = getStringCellValue(row.getCell(0));
            String destinationPrefixName = getStringCellValue(row.getCell(1));
            double rate = row.getCell(2).getNumericCellValue();
            String startTimeStr = getStringCellValue(row.getCell(3));
            String endTimeStr = getStringCellValue(row.getCell(4));

            RateDetailDTO.RateDetailDTOBuilder builder = RateDetailDTO.builder()
                    .destinationPrefix(destinationPrefix)
                    .destinationPrefixName(destinationPrefixName)
                    .rate(rate)
                    .startTime(parseDateTime(startTimeStr))
                    .endTime(parseDateTime(endTimeStr))
                    .ratePackageId(ratePackageId);

            // Add source fields if present
            if (row.getPhysicalNumberOfCells() > 6) {
                builder.sourcePrefix(getStringCellValue(row.getCell(5)))
                        .sourcePrefixName(getStringCellValue(row.getCell(6)));
            }

            return builder.build();
        } catch (Exception e) {
            int rowNum = row.getRowNum() + 1;
            throw new IllegalArgumentException("Error in row " + rowNum + ": " + e.getMessage());
        }
    }

    /**
     * Get string value from Excel cell
     */
    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            throw new IllegalArgumentException("Cell cannot be null");
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DATE_FORMATTERS[0].format(cell.getLocalDateTimeCellValue());
                }
                return String.valueOf((long) cell.getNumericCellValue());
            default:
                throw new IllegalArgumentException("Unsupported cell type");
        }
    }

    /**
     * Validate CSV header
     */
    private void validateCSVHeader(String[] header) {
        if (header == null || header.length < 5) {
            throw new IllegalArgumentException("Invalid CSV header format");
        }

        // Check for minimum required columns
        if (!header[0].equalsIgnoreCase("destinationPrefix") ||
                !header[1].equalsIgnoreCase("destinationPrefixName") ||
                !header[2].equalsIgnoreCase("rate") ||
                !header[3].equalsIgnoreCase("startTime") ||
                !header[4].equalsIgnoreCase("endTime")) {
            throw new IllegalArgumentException(
                    "Invalid CSV header format. Expected: destinationPrefix,destinationPrefixName,rate,startTime,endTime");
        }

        // Check for source columns if present
        if (header.length > 5) {
            if (!header[5].equalsIgnoreCase("sourcePrefix") ||
                    !header[6].equalsIgnoreCase("sourcePrefixName")) {
                throw new IllegalArgumentException(
                        "Invalid CSV header format for source-destination based rates. " +
                                "Expected: destinationPrefix,destinationPrefixName,rate,startTime,endTime,sourcePrefix,sourcePrefixName");
            }
        }
    }
}