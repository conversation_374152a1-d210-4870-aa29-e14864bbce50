package com.xcess.ocs.service;

import com.xcess.ocs.dto.PageResponseDTO;
import com.xcess.ocs.dto.PrefixDTO;
import com.xcess.ocs.dto.search.PrefixSearchDTO;
import com.xcess.ocs.entity.Country;
import com.xcess.ocs.entity.Prefix;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.mapper.PrefixMapper;
import com.xcess.ocs.repository.CountryRepository;
import com.xcess.ocs.repository.PrefixRepository;
import com.xcess.ocs.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PrefixService {

    private final PrefixRepository prefixRepository;
    private final CountryRepository countryRepository;
    private final PrefixMapper prefixMapper;

    /**
     * Get all prefixes
     */
    public List<PrefixDTO> getAllPrefixes() {
        log.debug("Fetching all prefixes");
        List<PrefixDTO> prefixes = prefixRepository.findAll().stream()
                .map(prefixMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} prefixes", prefixes.size());
        return prefixes;
    }

    /**
     * Get prefixes in pages
     */
    public PageResponseDTO<PrefixDTO> getPrefixesInPage(Pageable pageable) {
        log.debug("Fetching prefixes in pages");
        Page<Prefix> prefixesPage = prefixRepository.findAll(pageable);
        List<PrefixDTO> prefixes = prefixesPage.getContent().stream()
                .map(prefixMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} prefixes in page", prefixes.size());
        return PaginationUtils.buildGetResponseDTO(prefixes, prefixesPage);
    }

    /**
     * Search prefixes with optional search term and fields
     */
    public PageResponseDTO<PrefixDTO> searchPrefixes(PrefixSearchDTO searchDTO, Pageable pageable) {
        log.debug("Searching prefixes with searchTerm: {}", searchDTO.getSearchTerm());

        Page<Prefix> prefixPage = prefixRepository.searchPrefixes(
                searchDTO.getSearchTerm(),
                pageable);

        List<PrefixDTO> prefixes = prefixPage.getContent().stream()
                .map(prefixMapper::toDto)
                .collect(Collectors.toList());

        log.debug("Found {} prefixes matching criteria", prefixes.size());
        return PaginationUtils.buildGetResponseDTO(prefixes, prefixPage);
    }

    /**
     * Get prefix by ID
     */
    public PrefixDTO getPrefixById(Long id) {
        log.debug("Fetching prefix with ID: {}", id);
        Prefix prefix = prefixRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Prefix not found with ID: {}", id);
                    return new ResourceNotFoundException("Prefix not found with ID: " + id);
                });
        return prefixMapper.toDto(prefix);
    }

    /**
     * Create new prefix with validation
     */
    public PrefixDTO createPrefix(PrefixDTO prefixDTO) {
        log.info("Creating new prefix: {}", prefixDTO.getPrefix());
        if (prefixRepository.existsByPrefixAndIsDeletedFalse(prefixDTO.getPrefix())) {
            log.warn("Prefix already exists: {}", prefixDTO.getPrefix());
            throw new IllegalArgumentException(
                    String.format("Prefix '%s' already exists", prefixDTO.getPrefix()));
        }

        Country country = countryRepository.findByName(prefixDTO.getCountryName())
                .orElseThrow(() -> {
                    log.warn("Country not found with name: {}", prefixDTO.getCountryName());
                    return new ResourceNotFoundException("Country not found with name: " + prefixDTO.getCountryName());
                });

        validatePrefixFormat(prefixDTO.getPrefix(), country.getCountryCode());

        Prefix prefix = prefixMapper.toEntity(prefixDTO, country);
        prefix = prefixRepository.save(prefix);
        log.info("Successfully created prefix with ID: {}", prefix.getPrefixId());
        return prefixMapper.toDto(prefix);
    }

    /**
     * Update existing prefix with validation
     */
    public PrefixDTO updatePrefix(Long id, PrefixDTO prefixDTO) {
        log.info("Updating prefix with ID: {}", id);
        Prefix existingPrefix = prefixRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Prefix not found with ID: {}", id);
                    return new ResourceNotFoundException("Prefix not found with ID: " + id);
                });

        if (!existingPrefix.getPrefix().equalsIgnoreCase(prefixDTO.getPrefix()) &&
                prefixRepository.existsByPrefixAndIsDeletedFalse(prefixDTO.getPrefix())) {
            log.warn("Prefix already exists: {}", prefixDTO.getPrefix());
            throw new IllegalArgumentException(
                    String.format("Prefix '%s' already exists", prefixDTO.getPrefix()));
        }

        Country country = countryRepository.findByName(prefixDTO.getCountryName())
                .orElseThrow(() -> {
                    log.warn("Country not found with name: {}", prefixDTO.getCountryName());
                    return new ResourceNotFoundException(
                            "Country not found with name: " + prefixDTO.getCountryName());
                });

        validatePrefixFormat(prefixDTO.getPrefix(), country.getCountryCode());

        existingPrefix.setPrefix(prefixDTO.getPrefix());
        existingPrefix.setCountry(country);
        existingPrefix.setPrefixName(prefixDTO.getPrefixName());

        existingPrefix = prefixRepository.save(existingPrefix);
        log.info("Successfully updated prefix with ID: {}", id);
        return prefixMapper.toDto(existingPrefix);
    }

    /**
     * Delete prefix by ID
     */
    public void deletePrefix(Long id) {
        log.info("Deleting prefix with ID: {}", id);
        if (!prefixRepository.existsById(id)) {
            log.warn("Attempt to delete non-existent prefix with ID: {}", id);
            throw new ResourceNotFoundException("Prefix not found with ID: " + id);
        }
        prefixRepository.deleteById(id);
        log.info("Successfully deleted prefix with ID: {}", id);
    }

    /**
     * Get all prefixes by country name
     */
    public List<PrefixDTO> getPrefixesByCountryName(String countryName) {
        log.debug("Fetching prefixes for country: {}", countryName);
        Country country = countryRepository.findByName(countryName)
                .orElseThrow(() -> {
                    log.warn("Country not found with name: {}", countryName);
                    return new ResourceNotFoundException("Country not found with name: " + countryName);
                });
        List<PrefixDTO> prefixes = prefixRepository.findByCountryCountryId(country.getCountryId()).stream()
                .map(prefixMapper::toDto)
                .collect(Collectors.toList());
        log.debug("Retrieved {} prefixes for country: {}", prefixes.size(), countryName);
        return prefixes;
    }

    /**
     * Validate that prefix starts with country code (without '+')
     */
    private void validatePrefixFormat(String prefix, String countryCode) {
        log.debug("Validating prefix format: {} against country code: {}", prefix, countryCode);
        String cleanCountryCode = countryCode.replace("+", "");
        if (!prefix.startsWith(cleanCountryCode)) {
            log.warn("Invalid prefix format. Prefix {} must start with country code {}", prefix, cleanCountryCode);
            throw new IllegalArgumentException(
                    String.format("Prefix '%s' must start with '%s'", prefix, cleanCountryCode));
        }
    }

    public List<PrefixDTO> getPrefixesByCountryId(Long countryId) {
        Country country = countryRepository.findById(countryId)
                .orElseThrow(() -> new ResourceNotFoundException("Country not found with ID: " + countryId));
        return prefixRepository.findByCountryCountryId(country.getCountryId()).stream()
                .map(prefixMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Update prefix fields without requiring countryName
     */
    public PrefixDTO updatePrefixFieldsOnly(Long prefixId, Map<String, Object> fields) {
        // Find the existing prefix
        Prefix existingPrefix = prefixRepository.findById(prefixId)
                .orElseThrow(() -> new ResourceNotFoundException("Prefix not found with ID: " + prefixId));

        // Validate the new prefix format
        validatePrefixFormat(fields.get("prefix").toString(), existingPrefix.getCountry().getCountryCode());

        // Update only the necessary fields while keeping the existing ID and country
        existingPrefix.setPrefix(fields.get("prefix").toString());
        existingPrefix.setPrefixName(fields.get("prefixName").toString());

        // Save and return
        Prefix updatedPrefix = prefixRepository.save(existingPrefix);
        return prefixMapper.toDto(updatedPrefix);
    }
}