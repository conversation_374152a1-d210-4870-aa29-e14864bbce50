package com.xcess.ocs.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.RatedCdr;
import com.xcess.ocs.repository.RatedCdrRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RatedCdrService {

    private static final Logger logger = LoggerFactory.getLogger(RatedCdrService.class);

    private final RatedCdrRepository ratedCdrRepository;

    public RatedCdrService(RatedCdrRepository ratedCdrRepository) {
        this.ratedCdrRepository = ratedCdrRepository;
    }

    /**
     * NEW METHOD: Save an enhanced RatedCdr entity with rating information
     * This method handles the new flow where CDRs come pre-rated from the rating engine
     */
    public RatedCdr saveRatedCdr(RatedCdr ratedCdr) {
        try {
            RatedCdr savedEntity = ratedCdrRepository.save(ratedCdr);

            // Enhanced logging with rating information
            logger.info("Saved enhanced RatedCdr entity: id={}, calling={}, called={}, " +
                       "ratingStatus={}, appliedRate={}, ratePackage={}, duration={} minutes",
                    savedEntity.getRatedCdrId(),
                    savedEntity.getCallingNumber(),
                    savedEntity.getCalledNumber(),
                    savedEntity.getRatingStatus(),
                    savedEntity.getAppliedRate(),
                    savedEntity.getRatePackageName(),
                    savedEntity.getDurationMinutes());

            return savedEntity;

        } catch (Exception e) {
            logger.error("Failed to save enhanced RatedCdr: calling={}, called={}, " +
                        "ratingStatus={}, error={}",
                    ratedCdr.getCallingNumber(),
                    ratedCdr.getCalledNumber(),
                    ratedCdr.getRatingStatus(),
                    e.getMessage(), e);
            throw e;
        }
    }

    /**
     * LEGACY METHOD: Save CDR without rating (kept for backward compatibility)
     * NOTE: This method is now deprecated in favor of the rating-integrated flow
     */
    @Deprecated
    public void saveCdr(RatedCdrDTO record, Long sourceId) {
        try {
            RatedCdr entity = new RatedCdr();
            entity.setStartTime(record.getStartTime());
            entity.setEndTime(record.getEndTime());
            entity.setCallingNumber(record.getCallingNumber());
            entity.setCalledNumber(record.getCalledNumber());
            entity.setIncomingAccountId(record.getIncomingAccountId());
            entity.setOutgoingAccountId(record.getOutgoingAccountId());
            entity.setSourceId(sourceId);

            // Mark as pending since no rating was applied
            entity.markAsPending();

            ratedCdrRepository.save(entity);

            // Console output
            System.out.println("Saved RatedCdr (LEGACY - NO RATING): " + entity);

            // Production-grade logging
            logger.warn("Saved RatedCdr entity using LEGACY method (no rating applied) for sourceId {}: {}",
                       sourceId, entity);

        } catch (Exception e) {
            //  Console error
            System.err.println(" Error saving RatedCdr: " + e.getMessage());

            // Production-grade error logging
            logger.error(" Failed to save RatedCdr for sourceId {}. Error: {}", sourceId, e.getMessage(), e);

            throw e;
        }
    }
}
