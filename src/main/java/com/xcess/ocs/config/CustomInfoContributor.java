package com.xcess.ocs.config;

import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

@Component
public class CustomInfoContributor implements InfoContributor {

    @Override
    public void contribute(Info.Builder builder) {
        // Add custom details to the info endpoint response
        builder.withDetail("ProjectTitle", "A CPass Server")
                .withDetail("ProjectDescription", "This is a Cpass service having all the details of the services")
                .withDetail("Developer", "CPass Team");

    }
}