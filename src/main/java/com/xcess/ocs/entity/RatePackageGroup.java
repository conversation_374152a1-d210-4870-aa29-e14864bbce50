package com.xcess.ocs.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "rate_package_groups")
@SQLDelete(sql = "UPDATE rate_package_groups SET is_deleted = true WHERE rate_package_group_id = ?")
@Where(clause = "is_deleted = false")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RatePackageGroup extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long ratePackageGroupId;

    @Column(name = "name", nullable = false, unique = true)
    @NotEmpty(message = "Rate package group name is required")
    @Size(min = 6, max = 100, message = "Name must be between 6 and 100 characters")
    private String name;

    @Column(nullable = false)
    @NotBlank(message = "Description is required")
    @Size(min = 6, max = 100, message = "Description must be between 6 and 100 characters")
    private String description;

    @Column(nullable = false)
    @NotNull(message = "Package type is required")
    @Enumerated(EnumType.STRING)
    private PackageType packageType;

    @Builder.Default
    @OneToMany(mappedBy = "ratePackageGroup", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RatePackageAssociation> ratePackageAssociations = new ArrayList<>();

    public enum PackageType {
        SELLING("SELLING"),
        BUYING("BUYING");

        private final String value;

        PackageType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static PackageType fromString(String value) {
            for (PackageType type : PackageType.values()) {
                if (type.value.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Invalid PackageType: " + value);
        }
    }

    // Helper methods for managing associations
    public void addRatePackageAssociation(RatePackageAssociation association) {
        ratePackageAssociations.add(association);
        association.setRatePackageGroup(this);
    }

    public void removeRatePackageAssociation(RatePackageAssociation association) {
        ratePackageAssociations.remove(association);
        association.setRatePackageGroup(null);
    }
}
