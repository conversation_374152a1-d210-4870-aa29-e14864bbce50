package com.xcess.ocs.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "rated_cdr")
@SQLDelete(sql = "UPDATE rated_cdr SET is_deleted = true WHERE rated_cdr_id = ?")
@Where(clause = "is_deleted = false")
public class RatedCdr extends BaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long ratedCdrId;

    @Column(name = "calling_number", nullable = false)
    private String callingNumber;

    @Column(name = "called_number", nullable = false)
    private String calledNumber;

    @Column(name = "start_time", nullable = false)
    private String startTime;

    @Column(name = "end_time", nullable = false)
    private String endTime;

    @Column(name = "incoming_account_id", nullable = false)
    private String incomingAccountId;

    @Column(name = "outgoing_account_id", nullable = false)
    private String outgoingAccountId;

    @Column(name = "source_id", nullable = false)
    private Long sourceId;

    /**
     * The applied rate per unit (e.g., per minute)
     */
    @Column(name = "applied_rate", precision = 10, scale = 4)
    private BigDecimal appliedRate;

    /**
     * The ID of the rate package used for rating
     */
    @Column(name = "rate_package_id")
    private Long ratePackageId;

    /**
     * The name of the rate package used for rating
     */
    @Column(name = "rate_package_name")
    private String ratePackageName;

    /**
     * The source prefix that was matched during rating
     */
    @Column(name = "matched_source_prefix")
    private String matchedSourcePrefix;

    /**
     * The destination prefix that was matched during rating
     */
    @Column(name = "matched_destination_prefix")
    private String matchedDestinationPrefix;

    /**
     * Rating status: RATED, UNRATED, FAILED
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "rating_status")
    private RatingStatus ratingStatus;

    /**
     * Reason for unrated or failed status
     */
    @Column(name = "rating_failure_reason")
    private String ratingFailureReason;

    /**
     * Timestamp when rating was applied
     */
    @Column(name = "rated_at")
    private LocalDateTime ratedAt;

    /**
     * The ID of the specific rate detail used
     */
    @Column(name = "rate_detail_id")
    private Long rateDetailId;

    /**
     * Whether this was a source-destination match (Phase 1) or destination-only match (Phase 2)
     */
    @Column(name = "is_source_destination_match")
    private Boolean isSourceDestinationMatch;

    /**
     * Total calculated cost for this call (rate * duration)
     */
    @Column(name = "total_cost", precision = 10, scale = 4)
    private BigDecimal totalCost;

    /**
     * Call duration in minutes (calculated from start and end time)
     */
    @Column(name = "duration_minutes", precision = 10, scale = 2)
    private BigDecimal durationMinutes;

    // ========================================================================
    // RATING STATUS ENUM
    // ========================================================================

    /**
     * Enum representing the rating status of a CDR
     */
    public enum RatingStatus {
        /**
         * CDR has been successfully rated
         */
        RATED,

        /**
         * CDR could not be rated (no matching rate found)
         */
        UNRATED,

        /**
         * Rating process failed due to an error
         */
        FAILED,

        /**
         * CDR is pending rating (initial state)
         */
        PENDING
    }

    // ========================================================================
    // HELPER METHODS FOR RATING
    // ========================================================================

    /**
     * Mark this CDR as successfully rated
     */
    public void markAsRated(BigDecimal rate, Long ratePackageId, String ratePackageName,
                           Long rateDetailId, String sourcePrefix, String destinationPrefix,
                           boolean isSourceDestMatch) {
        this.appliedRate = rate;
        this.ratePackageId = ratePackageId;
        this.ratePackageName = ratePackageName;
        this.rateDetailId = rateDetailId;
        this.matchedSourcePrefix = sourcePrefix;
        this.matchedDestinationPrefix = destinationPrefix;
        this.isSourceDestinationMatch = isSourceDestMatch;
        this.ratingStatus = RatingStatus.RATED;
        this.ratedAt = LocalDateTime.now();
        this.ratingFailureReason = null;

        // Calculate total cost if duration is available
        if (this.durationMinutes != null && rate != null) {
            this.totalCost = rate.multiply(this.durationMinutes);
        }
    }

    /**
     * Mark this CDR as unrated
     */
    public void markAsUnrated(String reason) {
        this.ratingStatus = RatingStatus.UNRATED;
        this.ratingFailureReason = reason;
        this.ratedAt = LocalDateTime.now();
        this.appliedRate = null;
        this.totalCost = null;
    }

    /**
     * Mark this CDR as failed to rate
     */
    public void markAsFailed(String reason) {
        this.ratingStatus = RatingStatus.FAILED;
        this.ratingFailureReason = reason;
        this.ratedAt = LocalDateTime.now();
        this.appliedRate = null;
        this.totalCost = null;
    }

    /**
     * Set the CDR as pending rating (initial state)
     */
    public void markAsPending() {
        this.ratingStatus = RatingStatus.PENDING;
        this.ratedAt = null;
        this.ratingFailureReason = null;
    }

    /**
     * Check if this CDR has been rated
     */
    public boolean isRated() {
        return RatingStatus.RATED.equals(this.ratingStatus);
    }

    /**
     * Check if this CDR is unrated
     */
    public boolean isUnrated() {
        return RatingStatus.UNRATED.equals(this.ratingStatus);
    }

    /**
     * Check if rating failed
     */
    public boolean isRatingFailed() {
        return RatingStatus.FAILED.equals(this.ratingStatus);
    }
}