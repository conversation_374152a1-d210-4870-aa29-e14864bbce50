package com.xcess.ocs.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.SQLDelete;

import lombok.*;
import org.hibernate.annotations.Where;

@Getter
@Setter
@Entity
@Table(name = "partners")
@SQLDelete(sql = "UPDATE partners SET is_deleted = true WHERE partner_id = ?")
@Where(clause = "is_deleted = false")
@NoArgsConstructor
@AllArgsConstructor
public class Partner extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "partner_id")
    private Long partnerId;

    @Column(name = "partner_name", nullable = false)
    private String partnerName;

    @Column(name = "partner_type", nullable = false)
    private String partnerType; // "CUSTOMER", "VENDOR", "BOTH" as string
}
