package com.xcess.ocs.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;


@Entity
@Table(name = "prefixes")
@Getter
@Setter
@SQLDelete(sql = "UPDATE prefixes SET is_deleted = true WHERE prefix_id = ?")
@Where(clause = "is_deleted = false")
@NoArgsConstructor
@AllArgsConstructor
public class Prefix extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long prefixId;

    @ManyToOne
    @JoinColumn(name = "country_id", nullable = false)
//    @NotFound(action = NotFoundAction.IGNORE)  // Prevents error if soft deleting Country
    private Country country;

    @Column(nullable = false, unique = true)
    private String prefix;

    @Column(nullable = false)
    private String prefixName;
}