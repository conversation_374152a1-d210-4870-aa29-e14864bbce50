package com.xcess.ocs.mapper;

import com.xcess.ocs.dto.PartnerDTO;
import com.xcess.ocs.entity.Partner;
import org.springframework.stereotype.Component;

@Component
public class PartnerMapper {

    public PartnerDTO toDto(Partner partner) {
        if (partner == null) {
            return null;
        }
        PartnerDTO dto = new PartnerDTO();
        dto.setPartnerId(partner.getPartnerId());
        dto.setPartnerName(partner.getPartnerName());
        dto.setPartnerType(partner.getPartnerType());
        return dto;
    }

    public Partner toEntity(PartnerDTO dto) {
        if (dto == null) {
            return null;
        }
        Partner partner = new Partner();
        partner.setPartnerId(dto.getPartnerId());
        partner.setPartnerName(dto.getPartnerName());
        partner.setPartnerType(dto.getPartnerType());
        return partner;
    }
}