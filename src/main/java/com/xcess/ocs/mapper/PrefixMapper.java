package com.xcess.ocs.mapper;

import com.xcess.ocs.dto.PrefixDTO;
import com.xcess.ocs.entity.Prefix;
import com.xcess.ocs.entity.Country;
import org.springframework.stereotype.Component;

@Component
public class PrefixMapper {

    public PrefixDTO toDto(Prefix prefix) {
        if (prefix == null) {
            return null;
        }

        PrefixDTO dto = new PrefixDTO();
        dto.setPrefixId(prefix.getPrefixId());
        dto.setPrefix(prefix.getPrefix());
        dto.setPrefixName(prefix.getPrefixName());

        if (prefix.getCountry() != null) {
            dto.setCountryName(prefix.getCountry().getName());
        }

        return dto;
    }

    public Prefix toEntity(PrefixDTO dto, Country country) {
        if (dto == null) {
            return null;
        }

        Prefix prefix = new Prefix();
        prefix.setPrefixId(dto.getPrefixId());
        prefix.setPrefix(dto.getPrefix());
        prefix.setPrefixName(dto.getPrefixName());
        prefix.setCountry(country);

        return prefix;
    }
}