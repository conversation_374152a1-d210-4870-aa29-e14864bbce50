package com.xcess.ocs.mapper;

import com.xcess.ocs.dto.CountryDTO;
import com.xcess.ocs.entity.Country;
import org.springframework.stereotype.Component;

@Component
public class CountryMapper {

    public CountryDTO toDto(Country country) {
        if (country == null) {
            return null;
        }

        CountryDTO countryDTO = new CountryDTO();
        countryDTO.setCountryId(country.getCountryId());
        countryDTO.setName(country.getName());
        countryDTO.setCountryCode(country.getCountryCode());
        return countryDTO;
    }

    public Country toEntity(CountryDTO countryDTO) {
        if (countryDTO == null) {
            return null;
        }

        Country country = new Country();
        country.setCountryId(countryDTO.getCountryId());
        country.setName(countryDTO.getName());
        country.setCountryCode(countryDTO.getCountryCode());
        return country;
    }
}
