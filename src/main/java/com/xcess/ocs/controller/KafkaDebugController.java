package com.xcess.ocs.controller;

import com.xcess.ocs.cache.SourceCdrConfigurationCache;
import com.xcess.ocs.cache.SourceConfigurationCache;
import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.dto.SourceCdrConfigurationDTO;
import com.xcess.ocs.dto.SourceConfigurationDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Debug controller for Kafka CDR field mapping issues.
 * Helps diagnose field count mismatches and configuration problems.
 */
@Slf4j
@RestController
@RequestMapping("/api/kafka-debug")
@RequiredArgsConstructor
@Tag(name = "Kafka Debug", description = "Debug Kafka CDR field mapping issues")
public class KafkaDebugController {
    
    private final SourceConfigurationCache sourceConfigCache;
    private final SourceCdrConfigurationCache sourceCdrConfigCache;
    
    /**
     * Debug field configuration for a specific topic
     */
    @GetMapping("/field-config/{topicName}")
    @Operation(summary = "Get Field Configuration", 
               description = "Get the field configuration for a specific Kafka topic")
    public ResponseEntity<Map<String, Object>> getFieldConfig(
            @Parameter(description = "Kafka topic name") 
            @PathVariable String topicName) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("topicName", topicName);
        
        try {
            // Get source configuration
            SourceConfigurationDTO sourceConfig = sourceConfigCache.getConfigurationByTopicName(topicName);
            if (sourceConfig == null) {
                result.put("error", "Source configuration not found for topic: " + topicName);
                return ResponseEntity.badRequest().body(result);
            }
            
            result.put("sourceId", sourceConfig.getSourceId());
            result.put("sourceName", sourceConfig.getSourceName());
            
            // Get field configurations
            List<SourceCdrConfigurationDTO> fieldConfigs = sourceCdrConfigCache.getConfigurationsBySourceId(sourceConfig.getSourceId());
            
            if (fieldConfigs.isEmpty()) {
                result.put("error", "No field configuration found for source ID: " + sourceConfig.getSourceId());
                return ResponseEntity.badRequest().body(result);
            }
            
            // Sort by sequence and create field order
            List<String> fieldOrder = fieldConfigs.stream()
                    .sorted(Comparator.comparing(SourceCdrConfigurationDTO::getSequence))
                    .map(SourceCdrConfigurationDTO::getFieldName)
                    .toList();
            
            result.put("expectedFieldCount", fieldOrder.size());
            result.put("fieldOrder", fieldOrder);
            result.put("fieldConfigurations", fieldConfigs);
            
            // Check for duplicate sequences
            Map<Integer, Long> sequenceCounts = fieldConfigs.stream()
                    .collect(Collectors.groupingBy(SourceCdrConfigurationDTO::getSequence, Collectors.counting()));
            
            Set<Integer> duplicateSequences = sequenceCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            
            if (!duplicateSequences.isEmpty()) {
                result.put("warning", "Duplicate sequences found: " + duplicateSequences);
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error getting field configuration for topic: " + topicName, e);
            result.put("error", "Failed to get field configuration: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Test message parsing with a sample message
     */
    @PostMapping("/test-parse/{topicName}")
    @Operation(summary = "Test Message Parsing", 
               description = "Test parsing a sample Kafka message for a specific topic")
    public ResponseEntity<Map<String, Object>> testMessageParsing(
            @Parameter(description = "Kafka topic name") 
            @PathVariable String topicName,
            
            @Parameter(description = "Sample comma-separated message", 
                      example = "2024-01-15 10:30:00,2024-01-15 10:35:00,12345678901,919876543210,ACC001,ACC002")
            @RequestBody String sampleMessage) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("topicName", topicName);
        result.put("sampleMessage", sampleMessage);
        
        try {
            // Get source configuration
            SourceConfigurationDTO sourceConfig = sourceConfigCache.getConfigurationByTopicName(topicName);
            if (sourceConfig == null) {
                result.put("error", "Source configuration not found for topic: " + topicName);
                return ResponseEntity.badRequest().body(result);
            }
            
            // Get field configurations
            List<SourceCdrConfigurationDTO> fieldConfigs = sourceCdrConfigCache.getConfigurationsBySourceId(sourceConfig.getSourceId());
            
            if (fieldConfigs.isEmpty()) {
                result.put("error", "No field configuration found for source ID: " + sourceConfig.getSourceId());
                return ResponseEntity.badRequest().body(result);
            }
            
            // Sort by sequence and create field order
            List<String> fieldOrder = fieldConfigs.stream()
                    .sorted(Comparator.comparing(SourceCdrConfigurationDTO::getSequence))
                    .map(SourceCdrConfigurationDTO::getFieldName)
                    .toList();
            
            result.put("expectedFieldCount", fieldOrder.size());
            result.put("fieldOrder", fieldOrder);
            
            // Parse the message
            String[] fields = sampleMessage.split(",");
            result.put("actualFieldCount", fields.length);
            result.put("parsedFields", Arrays.asList(fields));
            
            // Check field count match
            if (fields.length != fieldOrder.size()) {
                result.put("error", "Field count mismatch: expected " + fieldOrder.size() + " but got " + fields.length);
                result.put("fieldCountMatch", false);
            } else {
                result.put("fieldCountMatch", true);
                
                // Try to parse into DTO
                RatedCdrDTO cdrRecord = parseMessageToDto(sampleMessage, fieldOrder);
                result.put("parsedDTO", cdrRecord);
                result.put("parseSuccess", true);
            }
            
            // Create field mapping for visualization
            Map<String, String> fieldMapping = new HashMap<>();
            for (int i = 0; i < Math.min(fields.length, fieldOrder.size()); i++) {
                fieldMapping.put(fieldOrder.get(i), fields[i].trim());
            }
            result.put("fieldMapping", fieldMapping);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error testing message parsing for topic: " + topicName, e);
            result.put("error", "Failed to parse message: " + e.getMessage());
            result.put("parseSuccess", false);
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Get all available topics and their configurations
     */
    @GetMapping("/all-topics")
    @Operation(summary = "Get All Topics", 
               description = "Get all available Kafka topics and their field configurations")
    public ResponseEntity<Map<String, Object>> getAllTopics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // This would need to be implemented based on your cache structure
            // For now, return a placeholder
            result.put("message", "This endpoint needs to be implemented based on your SourceConfigurationCache structure");
            result.put("note", "You can use /field-config/{topicName} for specific topics");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error getting all topics", e);
            result.put("error", "Failed to get topics: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Helper method to parse message into DTO (same logic as MessageProcessor)
     */
    private RatedCdrDTO parseMessageToDto(String message, List<String> fieldOrder) {
        String[] fields = message.split(",");
        
        if (fields.length != fieldOrder.size()) {
            throw new IllegalArgumentException("Message field count does not match configuration: " +
                    "expected " + fieldOrder.size() + " fields but got " + fields.length);
        }

        RatedCdrDTO cdrRecord = new RatedCdrDTO();
        
        for (int i = 0; i < fieldOrder.size(); i++) {
            String value = fields[i].trim();
            switch (fieldOrder.get(i).toUpperCase()) {
                case "CALLING_NUMBER" -> cdrRecord.setCallingNumber(value);
                case "CALLED_NUMBER" -> cdrRecord.setCalledNumber(value);
                case "START_TIME" -> cdrRecord.setStartTime(value);
                case "END_TIME" -> cdrRecord.setEndTime(value);
                case "INCOMING_ACCOUNT_ID" -> cdrRecord.setIncomingAccountId(value);
                case "OUTGOING_ACCOUNT_ID" -> cdrRecord.setOutgoingAccountId(value);
            }
        }
        return cdrRecord;
    }
}
