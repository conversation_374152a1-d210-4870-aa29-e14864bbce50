package com.xcess.ocs.controller;

import com.xcess.ocs.cache.RatePackageCache;
import com.xcess.ocs.ratingengine.service.RateLookupService;
import com.xcess.ocs.ratingengine.service.TrieInitializationService;
import com.xcess.ocs.service.CdrRatingIntegrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST Controller for monitoring and managing the rating engine integration.
 * Provides endpoints for checking status, debugging rate lookups, and managing
 * the RadixTrie-based rating system.
 * 
 * <AUTHOR> Developer
 */
@Slf4j
@RestController
@RequestMapping("/api/rating-engine")
@RequiredArgsConstructor
@Tag(name = "Rating Engine", description = "Rating Engine Integration Management")
public class RatingEngineController {
    
    private final TrieInitializationService trieInitializationService;
    private final RateLookupService rateLookupService;
    private final CdrRatingIntegrationService cdrRatingIntegrationService;
    private final RatePackageCache ratePackageCache;
    
    /**
     * Get the current status of the rating engine
     */
    @GetMapping("/status")
    @Operation(summary = "Get Rating Engine Status", 
               description = "Returns the current status of the RadixTrie-based rating engine")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("engineReady", trieInitializationService.isEngineReady());
        status.put("detailedStatus", trieInitializationService.getInitializationStatus());
        
        status.put("statisticsNote", "Per-package trie statistics are now managed within RatePackageCache.");
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * Force re-initialization of the rating engine tries (refreshes the cache)
     */
    @PostMapping("/reinitialize")
    @Operation(summary = "Reinitialize Rating Engine Cache", 
               description = "Force refresh the cache, rebuilding RadixTrie data structures with latest rate data")
    public ResponseEntity<Map<String, String>> reinitialize() {
        try {
            log.info("Manual reinitialize/cache refresh request received for rating engine");
            trieInitializationService.refreshTriesAndCache();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Rating engine cache refresh initiated");
            response.put("status", "REFRESH_INITIATED");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to reinitialize/refresh rating engine cache", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to reinitialize/refresh rating engine cache: " + e.getMessage());
            response.put("status", "ERROR");
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Test rate lookup for debugging purposes
     */
    @GetMapping("/test-lookup")
    @Operation(summary = "Test Rate Lookup", 
               description = "Test the RadixTrie rate lookup algorithm with specific numbers")
    public ResponseEntity<Map<String, Object>> testRateLookup(
            @Parameter(description = "Source phone number", example = "12345678901")
            @RequestParam String sourceNumber,
            
            @Parameter(description = "Destination phone number", example = "919876543210") 
            @RequestParam String destinationNumber,
            
            @Parameter(description = "Call timestamp (optional, defaults to now)", example = "2024-01-15T10:30:00")
            @RequestParam(required = false) String callTimestamp) {
        
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("sourceNumber", sourceNumber);
            result.put("destinationNumber", destinationNumber);
            result.put("callTimestamp", callTimestamp != null ? callTimestamp : "now");
            
            if (!trieInitializationService.isEngineReady()) {
                result.put("error", "Rating engine not ready or cache not initialized");
                return ResponseEntity.badRequest().body(result);
            }
            
            // Get detailed lookup information
            String detailedInfo = cdrRatingIntegrationService.getDetailedRatingInfo(
                    sourceNumber, destinationNumber, 
                    callTimestamp != null ? callTimestamp : java.time.LocalDateTime.now().toString());
            
            result.put("detailedLookupInfo", detailedInfo);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error during test rate lookup", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("error", "Test lookup failed: " + e.getMessage());
            result.put("sourceNumber", sourceNumber);
            result.put("destinationNumber", destinationNumber);
            
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * Get rating engine statistics (now shows per-package trie stats)
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get Rating Engine Statistics", 
               description = "Get detailed statistics for each rate package's RadixTrie data structures")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        if (!trieInitializationService.isEngineReady()) {
            response.put("error", "Rating engine not ready or cache not initialized");
            return ResponseEntity.badRequest().body(response);
        }
        
        try {
            Map<String, String> packageTrieStats = ratePackageCache.getAllRatePackageTries().stream()
                .collect(Collectors.toMap(
                    tries -> "PackageTries_" + tries.hashCode(),
                    com.xcess.ocs.ratingengine.cache.RatePackageTries::getStatistics
                ));

            response.put("perPackageTrieStatistics", packageTrieStats);
            response.put("engineReady", true);
            response.put("detailedStatus", trieInitializationService.getInitializationStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error getting rating engine statistics", e);
            response.put("error", "Failed to get statistics: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Clear all trie data (now initiates a full cache refresh)
     */
    @PostMapping("/clear")
    @Operation(summary = "Refresh Rating Engine Cache", 
               description = "Clears and reloads all rate package data, including RadixTrie structures")
    public ResponseEntity<Map<String, String>> clearTries() {
        try {
            log.warn("Manual clear/refresh request received for rating engine cache");
            trieInitializationService.refreshTriesAndCache();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Rating engine cache refresh initiated");
            response.put("status", "REFRESH_INITIATED");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to refresh rating engine cache", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to refresh cache: " + e.getMessage());
            response.put("status", "ERROR");
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Health check endpoint for the rating engine
     */
    @GetMapping("/health")
    @Operation(summary = "Rating Engine Health Check", 
               description = "Simple health check for the rating engine")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        boolean isHealthy = trieInitializationService.isEngineReady();
        
        health.put("healthy", isHealthy);
        health.put("engineReady", trieInitializationService.isEngineReady());
        health.put("detailedStatus", trieInitializationService.getInitializationStatus());
        health.put("timestamp", java.time.LocalDateTime.now());
        
        if (isHealthy) {
            return ResponseEntity.ok(health);
        } else {
            return ResponseEntity.status(503).body(health);
        }
    }
}
