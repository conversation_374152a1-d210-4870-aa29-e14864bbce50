package com.xcess.ocs.controller;

import com.xcess.ocs.dto.*;
import com.xcess.ocs.dto.search.PartnerSearchDTO;
import com.xcess.ocs.service.PartnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/partners")
@CrossOrigin(origins = "*")
@Tag(name = "1. Partners", description = "Endpoints for managing partners")
@RequiredArgsConstructor
@Slf4j
public class PartnerController {

    private final PartnerService partnerService;

    @Operation(summary = "Create a new partner", description = "Creates a new partner and returns the created partner")
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "HTTP Status CREATED"),
            @ApiResponse(responseCode = "400", description = "HTTP Status BAD REQUEST", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class))),
            @ApiResponse(responseCode = "500", description = "HTTP Status INTERNAL SERVER ERROR", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PostMapping
    public ResponseEntity<PartnerDTO> createPartner(@Valid @RequestBody PartnerDTO partnerDTO) {
        log.info("REST request to create partner: {}", partnerDTO.getPartnerName());
        PartnerDTO createdPartner = partnerService.createPartner(partnerDTO);
        log.info("Partner created successfully with ID: {}", createdPartner.getPartnerId());
        return new ResponseEntity<>(createdPartner, HttpStatus.CREATED);
    }

    @Operation(summary = "Get all partners", description = "Returns a list of all partners")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "400", description = "HTTP Status BAD REQUEST",
                    content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @GetMapping
    public ResponseEntity<List<PartnerDTO>> getAllPartners() {
        log.info("REST request to get all partners");
        List<PartnerDTO> partners = partnerService.getAllPartners();
        log.info("Retrieved {} partners", partners.size());
        return ResponseEntity.ok(partners);
    }

    @Operation(summary = "Get paginated and filtered list of partners",
            description = "Returns a paginated list of partners with search functionality")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "400", description = "HTTP Status BAD REQUEST",
                    content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PostMapping("/paginated")
    public ResponseEntity<PageResponseDTO<PartnerDTO>> getPartnersInPage(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    required = true,
                    description = "Pagination request with partner search criteria",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(
                                    implementation = PageRequestDTO.class
                            ),
                            examples = @ExampleObject(
                                    name = "Search All Fields",
                                    summary = "Search by partner name or type",
                                    value = """
                                                {
                                                    "page": 1,
                                                    "pageSize": 5,
                                                    "searchCriteria": {
                                                        "partnerName": "John",
                                                        "partnerType": "CUSTOMER"
                                                    }
                                                }
                                                """
                            )
                    )
            )
            @Valid @RequestBody PageRequestDTO<PartnerSearchDTO> pageRequestDTO) {
        log.info("REST request to get partners in page with search criteria");

        Pageable pageable = PageRequest.of(pageRequestDTO.getPage() - 1, pageRequestDTO.getPageSize());
        PageResponseDTO<PartnerDTO> response;

        if (pageRequestDTO.getSearchCriteria() != null) {
            response = partnerService.searchPartners(pageRequestDTO.getSearchCriteria(), pageable);
            log.info("Retrieved filtered partners in a page");
        } else {
            response = partnerService.getPartnersInPage(pageable);
            log.info("Retrieved all partners in a page");
        }

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get partner by ID", description = "Returns a partner by its ID")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @GetMapping("/{id}")
    public ResponseEntity<PartnerDTO> getPartnerById(@PathVariable Long id) {
        log.info("REST request to get partner by ID: {}", id);
        PartnerDTO partner = partnerService.getPartnerById(id);
        return ResponseEntity.ok(partner);
    }

    @Operation(summary = "Update partner", description = "Updates an existing partner")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "HTTP Status OK"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @PutMapping("/{id}")
    public ResponseEntity<PartnerDTO> updatePartner(@PathVariable Long id, @Valid @RequestBody PartnerDTO partnerDTO) {
        log.info("REST request to update partner with ID: {}", id);
        PartnerDTO updatedPartner = partnerService.updatePartner(id, partnerDTO);
        return ResponseEntity.ok(updatedPartner);
    }

    @Operation(summary = "Delete partner", description = "Deletes a partner by its ID")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "HTTP Status NO CONTENT"),
            @ApiResponse(responseCode = "404", description = "HTTP Status NOT FOUND", content = @Content(schema = @Schema(implementation = ErrorResponseDTO.class)))
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePartner(@PathVariable Long id) {
        log.info("REST request to delete partner with ID: {}", id);
        partnerService.deletePartner(id);
        return ResponseEntity.noContent().build();
    }
}