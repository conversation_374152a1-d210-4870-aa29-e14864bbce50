package com.xcess.ocs.ratingengine.cache;

import com.xcess.ocs.ratingengine.trie.RadixDestinationOnlyTrie;
import com.xcess.ocs.ratingengine.trie.RadixSourceDestinationTrie;

/**
 * Container to hold the source-destination trie and destination-only trie 
 * for a specific rate package.
 */
public class RatePackageTries {
    private final RadixSourceDestinationTrie sourceDestinationTrie;
    private final RadixDestinationOnlyTrie destinationOnlyTrie;

    public RatePackageTries() {
        this.sourceDestinationTrie = new RadixSourceDestinationTrie();
        this.destinationOnlyTrie = new RadixDestinationOnlyTrie();
    }

    public RadixSourceDestinationTrie getSourceDestinationTrie() {
        return sourceDestinationTrie;
    }

    public RadixDestinationOnlyTrie getDestinationOnlyTrie() {
        return destinationOnlyTrie;
    }

    /**
     * Clears both tries contained within this holder.
     */
    public void clear() {
        if (sourceDestinationTrie != null) {
            sourceDestinationTrie.clear();
        }
        if (destinationOnlyTrie != null) {
            destinationOnlyTrie.clear();
        }
    }
    
    /**
     * Gets combined statistics from both tries.
     * @return A string containing statistics for both tries.
     */
    public String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("RatePackageTries Internal Stats:\n");
        if (sourceDestinationTrie != null) {
            stats.append("  ").append(sourceDestinationTrie.getStatistics()).append("\n");
        }
        if (destinationOnlyTrie != null) {
            stats.append("  ").append(destinationOnlyTrie.getStatistics());
        }
        return stats.toString();
    }
} 