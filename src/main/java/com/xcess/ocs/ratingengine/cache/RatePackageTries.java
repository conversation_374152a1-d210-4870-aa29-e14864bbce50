package com.xcess.ocs.ratingengine.cache;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import com.xcess.ocs.ratingengine.trie.RatePackageDestinationOnlyTrie;
import com.xcess.ocs.ratingengine.trie.RatePackageSourceDestinationTrie;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Container to hold the source-destination trie and destination-only trie 
 * for a specific rate package. Follows OOP principles with proper encapsulation.
 * 
 * Key OOP Concepts Applied:
 * - Encapsulation: Private fields with controlled access
 * - Composition: Contains specialized trie instances
 * - Single Responsibility: Manages tries for one rate package
 * - Delegation: Delegates operations to appropriate trie
 * 
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Getter
public class RatePackageTries {
    
    private final Long ratePackageId;
    private final RatePackageSourceDestinationTrie sourceDestinationTrie;
    private final RatePackageDestinationOnlyTrie destinationOnlyTrie;
    
    /**
     * Constructor for rate package specific tries
     * 
     * @param ratePackageId The rate package ID these tries belong to
     */
    public RatePackageTries(Long ratePackageId) {
        this.ratePackageId = ratePackageId;
        this.sourceDestinationTrie = new RatePackageSourceDestinationTrie(ratePackageId);
        this.destinationOnlyTrie = new RatePackageDestinationOnlyTrie(ratePackageId);
        
        log.debug("Created RatePackageTries for rate package: {}", ratePackageId);
    }
    
    /**
     * Insert a rate detail into the appropriate trie based on its characteristics
     * Follows the Strategy pattern - different insertion strategies based on rate detail type
     * 
     * @param rateDetail The rate detail to insert
     */
    public void insertRateDetail(RateDetails rateDetail) {
        if (rateDetail == null) {
            log.warn("Cannot insert null rate detail for package {}", ratePackageId);
            return;
        }
        
        // Validate rate detail belongs to this package
        if (rateDetail.getRatePackage() == null || 
            !rateDetail.getRatePackage().getRatePackageId().equals(ratePackageId)) {
            log.warn("Rate detail {} does not belong to package {}", 
                    rateDetail.getRateDetailsId(), ratePackageId);
            return;
        }
        
        // Strategy pattern: Choose insertion strategy based on rate detail characteristics
        if (hasSourceAndDestination(rateDetail)) {
            sourceDestinationTrie.insert(rateDetail);
            log.debug("Inserted source-destination rate detail {} into package {}", 
                    rateDetail.getRateDetailsId(), ratePackageId);
        } else if (hasDestinationOnly(rateDetail)) {
            destinationOnlyTrie.insert(rateDetail);
            log.debug("Inserted destination-only rate detail {} into package {}", 
                    rateDetail.getRateDetailsId(), ratePackageId);
        } else {
            log.warn("Rate detail {} has insufficient prefix information for package {}", 
                    rateDetail.getRateDetailsId(), ratePackageId);
        }
    }
    
    /**
     * Search for rate candidates across both tries using the two-phase algorithm
     * 
     * Phase 1: Source-destination matching (preferred)
     * Phase 2: Destination-only matching (fallback)
     * 
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @return List of all matching rate candidates from both phases
     */
    public List<RateCandidate> searchAllCandidates(String sourceNumber, String destinationNumber) {
        List<RateCandidate> allCandidates = new ArrayList<>();
        
        // Phase 1: Source-destination matching
        List<RateCandidate> phase1Candidates = sourceDestinationTrie.search(sourceNumber, destinationNumber);
        allCandidates.addAll(phase1Candidates);
        
        // Phase 2: Destination-only matching
        List<RateCandidate> phase2Candidates = destinationOnlyTrie.search(sourceNumber, destinationNumber);
        allCandidates.addAll(phase2Candidates);
        
        log.debug("Package {} search results: {} phase1 + {} phase2 = {} total candidates", 
                ratePackageId, phase1Candidates.size(), phase2Candidates.size(), allCandidates.size());
        
        return allCandidates;
    }
    
    /**
     * Search for rate candidates with preference for source-destination matches
     * Returns phase 1 results if available, otherwise phase 2 results
     * 
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @return List of preferred rate candidates
     */
    public List<RateCandidate> searchPreferred(String sourceNumber, String destinationNumber) {
        // Phase 1: Source-destination matching (preferred)
        List<RateCandidate> phase1Candidates = sourceDestinationTrie.search(sourceNumber, destinationNumber);
        
        if (!phase1Candidates.isEmpty()) {
            log.debug("Package {} returning {} preferred source-destination candidates", 
                    ratePackageId, phase1Candidates.size());
            return phase1Candidates;
        }
        
        // Phase 2: Destination-only matching (fallback)
        List<RateCandidate> phase2Candidates = destinationOnlyTrie.search(sourceNumber, destinationNumber);
        log.debug("Package {} returning {} fallback destination-only candidates", 
                ratePackageId, phase2Candidates.size());
        
        return phase2Candidates;
    }
    
    /**
     * Clear both tries contained within this holder
     */
    public void clear() {
        sourceDestinationTrie.clear();
        destinationOnlyTrie.clear();
        log.info("Cleared all tries for rate package: {}", ratePackageId);
    }
    
    /**
     * Get combined statistics from both tries
     * 
     * @return A string containing statistics for both tries
     */
    public String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append(String.format("RatePackageTries for Package %d:\n", ratePackageId));
        stats.append("  ").append(sourceDestinationTrie.getStatistics()).append("\n");
        stats.append("  ").append(destinationOnlyTrie.getStatistics());
        return stats.toString();
    }
    
    /**
     * Get total number of rate details across both tries
     * 
     * @return Total count of rate details
     */
    public int getTotalRateDetailsCount() {
        return sourceDestinationTrie.getTotalRateDetailsCount() + 
               destinationOnlyTrie.getTotalRateDetailsCount();
    }
    
    /**
     * Check if rate detail has both source and destination prefixes
     * 
     * @param rateDetail The rate detail to check
     * @return true if has both prefixes
     */
    private boolean hasSourceAndDestination(RateDetails rateDetail) {
        return rateDetail.getSourcePrefix() != null && 
               !rateDetail.getSourcePrefix().trim().isEmpty() &&
               rateDetail.getDestinationPrefix() != null && 
               !rateDetail.getDestinationPrefix().trim().isEmpty();
    }
    
    /**
     * Check if rate detail has destination prefix only (source is null)
     * 
     * @param rateDetail The rate detail to check
     * @return true if has destination only
     */
    private boolean hasDestinationOnly(RateDetails rateDetail) {
        return rateDetail.getSourcePrefix() == null &&
               rateDetail.getDestinationPrefix() != null && 
               !rateDetail.getDestinationPrefix().trim().isEmpty();
    }
    
    /**
     * Get rate package ID these tries belong to
     * 
     * @return The rate package ID
     */
    public Long getRatePackageId() {
        return ratePackageId;
    }
    
    /**
     * Check if this tries container is empty
     * 
     * @return true if both tries are empty
     */
    public boolean isEmpty() {
        return getTotalRateDetailsCount() == 0;
    }
    
    /**
     * Get detailed information about the tries for debugging
     * 
     * @return Detailed information string
     */
    public String getDetailedInfo() {
        StringBuilder info = new StringBuilder();
        info.append(String.format("=== RatePackageTries Details for Package %d ===\n", ratePackageId));
        info.append(String.format("Total Rate Details: %d\n", getTotalRateDetailsCount()));
        info.append(String.format("Source-Destination Trie: %d rate details\n", 
                sourceDestinationTrie.getTotalRateDetailsCount()));
        info.append(String.format("Destination-Only Trie: %d rate details\n", 
                destinationOnlyTrie.getTotalRateDetailsCount()));
        info.append("Source-Destination Trie Stats: ").append(sourceDestinationTrie.getStatistics()).append("\n");
        info.append("Destination-Only Trie Stats: ").append(destinationOnlyTrie.getStatistics());
        return info.toString();
    }
}
