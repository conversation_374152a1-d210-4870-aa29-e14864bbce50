package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**

 * Each node can store multiple rate details for the same prefix with different time ranges.
 * 
 * Key differences from simple trie:
 * - Edge labels can be strings of any length (compressed paths)
 * - Nodes with single children are compressed into their parent's edge
 * - Memory efficient for sparse tries with long common prefixes
 */
@Getter
@Setter
public class RadixTrieNode {
    
    /**
     * Children nodes mapped by the first character of their edge label
     * The edge label (compressed path) is stored in the child node
     */
    private final Map<Character, RadixTrieNode> children;
    
    /**
     * The edge label from parent to this node (compressed path)
     * For root node, this is null or empty
     */
    private String edgeLabel;
    
    /**
     * Indicates if this node represents the end of a valid prefix
     */
    private boolean isEndOfPrefix;
    
    /**
     * List of rate details associated with this prefix
     * Multiple entries can exist for different time ranges or rate packages
     */
    private final List<RateDetails> rateDetails;
    
    /**
     * The complete prefix string that this node represents
     * Only set for end-of-prefix nodes
     */
    private String prefix;
    
    /**
     * Default constructor for root node
     */
    public RadixTrieNode() {
        this.children = new ConcurrentHashMap<>();
        this.rateDetails = new ArrayList<>();
        this.isEndOfPrefix = false;
        this.edgeLabel = "";
        this.prefix = null;
    }
    
    /**
     * Constructor with edge label
     * 
     * @param edgeLabel The edge label from parent to this node
     */
    public RadixTrieNode(String edgeLabel) {
        this();
        this.edgeLabel = edgeLabel != null ? edgeLabel : "";
    }
    
    /**
     * Constructor with edge label and prefix
     * 
     * @param edgeLabel The edge label from parent to this node
     * @param prefix The complete prefix string this node represents
     */
    public RadixTrieNode(String edgeLabel, String prefix) {
        this(edgeLabel);
        this.prefix = prefix;
    }
    
    /**
     * Add a rate detail to this node
     * 
     * @param rateDetail The rate detail to add
     */
    public void addRateDetail(RateDetails rateDetail) {
        if (rateDetail != null) {
            this.rateDetails.add(rateDetail);
        }
    }
    
    /**
     * Add multiple rate details to this node
     * 
     * @param rateDetailsList List of rate details to add
     */
    public void addRateDetails(List<RateDetails> rateDetailsList) {
        if (rateDetailsList != null && !rateDetailsList.isEmpty()) {
            this.rateDetails.addAll(rateDetailsList);
        }
    }
    
    /**
     * Get a child node for the given character (first character of edge label)
     * 
     * @param ch The character to look for
     * @return The child node or null if not found
     */
    public RadixTrieNode getChild(char ch) {
        return children.get(ch);
    }
    
    /**
     * Add a child node for the given character
     * 
     * @param ch The character key (first character of child's edge label)
     * @param node The child node
     */
    public void addChild(char ch, RadixTrieNode node) {
        children.put(ch, node);
    }
    
    /**
     * Check if this node has any children
     * 
     * @return true if has children, false otherwise
     */
    public boolean hasChildren() {
        return !children.isEmpty();
    }
    
    /**
     * Get the children map
     * 
     * @return Map of children nodes
     */
    public Map<Character, RadixTrieNode> getChildren() {
        return children;
    }
    
    /**
     * Check if this node has any rate details
     * 
     * @return true if has rate details, false otherwise
     */
    public boolean hasRateDetails() {
        return !rateDetails.isEmpty();
    }
    
    /**
     * Get the count of rate details in this node
     * 
     * @return Number of rate details
     */
    public int getRateDetailsCount() {
        return rateDetails.size();
    }
    
    /**
     * Clear all rate details from this node
     */
    public void clearRateDetails() {
        rateDetails.clear();
    }
    
    /**
     * Get an unmodifiable view of the rate details
     * 
     * @return Unmodifiable list of rate details
     */
    public List<RateDetails> getRateDetailsUnmodifiable() {
        return Collections.unmodifiableList(rateDetails);
    }
    
    /**
     * Mark this node as end of prefix and set the prefix string
     * 
     * @param prefixString The complete prefix string
     */
    public void markAsEndOfPrefix(String prefixString) {
        this.isEndOfPrefix = true;
        this.prefix = prefixString;
    }
    
    /**
     * Get all children characters (first characters of edge labels)
     * 
     * @return Set of characters that have child nodes
     */
    public Set<Character> getChildrenKeys() {
        return children.keySet();
    }
    
    /**
     * Remove a child node
     * 
     * @param ch The character key to remove
     * @return The removed node or null if not found
     */
    public RadixTrieNode removeChild(char ch) {
        return children.remove(ch);
    }
    
    /**
     * Check if this node can be safely removed (no children and no rate details)
     * 
     * @return true if node can be removed, false otherwise
     */
    public boolean canBeRemoved() {
        return !hasChildren() && !hasRateDetails() && !isEndOfPrefix;
    }
    
    /**
     * Find the longest common prefix between two strings
     * 
     * @param str1 First string
     * @param str2 Second string
     * @return Length of longest common prefix
     */
    public static int findCommonPrefixLength(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0;
        }
        
        int minLength = Math.min(str1.length(), str2.length());
        int commonLength = 0;
        
        for (int i = 0; i < minLength; i++) {
            if (str1.charAt(i) == str2.charAt(i)) {
                commonLength++;
            } else {
                break;
            }
        }
        
        return commonLength;
    }
    
    /**
     * Check if this node needs to be split due to partial edge label match
     * 
     * @param searchString The string being searched
     * @param startIndex The starting index in the search string
     * @return true if node needs splitting, false otherwise
     */
    public boolean needsSplitting(String searchString, int startIndex) {
        if (edgeLabel == null || edgeLabel.isEmpty()) {
            return false;
        }
        
        if (startIndex >= searchString.length()) {
            return false;
        }
        
        String remainingSearch = searchString.substring(startIndex);
        int commonLength = findCommonPrefixLength(edgeLabel, remainingSearch);
        
        return commonLength > 0 && commonLength < edgeLabel.length();
    }
    
    @Override
    public String toString() {
        return String.format("RadixTrieNode{edgeLabel='%s', prefix='%s', isEndOfPrefix=%s, rateDetailsCount=%d, childrenCount=%d}", 
                edgeLabel, prefix, isEndOfPrefix, rateDetails.size(), children.size());
    }
}
