package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Radix <PERSON>e (Compressed Trie) implementation for destination-only based rate matching.
 * This radix trie stores rates where only destination prefix matters (source prefix is null).
 * The trie structure is: destinationPrefix -> RateDetails
 * 
 * Key improvements over simple trie:
 * - Compresses single-child paths to reduce memory usage
 * - Maintains same public API for compatibility
 * - Optimized for sparse tries with long common prefixes
 * 
 * <AUTHOR> Developer
 */
@Slf4j
@Component
public class RadixDestinationOnlyTrie {
    
    private final RadixTrieNode root;
    private final ReentrantReadWriteLock lock;
    
    public RadixDestinationOnlyTrie() {
        this.root = new RadixTrieNode();
        this.lock = new ReentrantReadWriteLock();
    }
    
    /**
     * Insert a rate detail into the radix trie
     * 
     * @param rateDetail The rate detail to insert (must have null sourcePrefix)
     */
    public void insert(RateDetails rateDetail) {
        if (rateDetail == null || rateDetail.getDestinationPrefix() == null) {
            log.warn("Cannot insert rate detail with null destination prefix: {}", rateDetail);
            return;
        }
        
        if (rateDetail.getSourcePrefix() != null) {
            log.warn("Destination-only radix trie received rate detail with non-null source prefix: {}", rateDetail);
            return;
        }
        
        String destinationPrefix = rateDetail.getDestinationPrefix();
        
        lock.writeLock().lock();
        try {
            insertRecursive(root, destinationPrefix, 0, rateDetail);
            log.debug("Inserted rate detail for destination prefix: {}", destinationPrefix);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Recursive method to insert a rate detail into the radix trie
     */
    private void insertRecursive(RadixTrieNode node, String prefix, int index, RateDetails rateDetail) {
        // If we've consumed the entire prefix, mark this node as end of prefix
        if (index >= prefix.length()) {
            node.markAsEndOfPrefix(prefix);
            node.addRateDetail(rateDetail);
            return;
        }
        
        char firstChar = prefix.charAt(index);
        RadixTrieNode child = node.getChild(firstChar);
        
        if (child == null) {
            // No child exists, create new node with remaining prefix as edge label
            String remainingPrefix = prefix.substring(index);
            RadixTrieNode newChild = new RadixTrieNode(remainingPrefix, prefix);
            newChild.markAsEndOfPrefix(prefix);
            newChild.addRateDetail(rateDetail);
            node.addChild(firstChar, newChild);
            return;
        }
        
        // Child exists, check edge label match
        String edgeLabel = child.getEdgeLabel();
        String remainingPrefix = prefix.substring(index);
        int commonLength = RadixTrieNode.findCommonPrefixLength(edgeLabel, remainingPrefix);
        
        if (commonLength == edgeLabel.length()) {
            // Full edge match, continue recursively
            insertRecursive(child, prefix, index + commonLength, rateDetail);
        } else if (commonLength > 0) {
            // Partial edge match, need to split the node
            splitNodeAndInsert(node, child, firstChar, commonLength, prefix, index, rateDetail);
        } else {
            // No match, this shouldn't happen if we got here via firstChar
            log.error("Unexpected state: no edge match for character {}", firstChar);
        }
    }
    
    /**
     * Split a node when there's a partial edge match and insert the new rate detail
     */
    private void splitNodeAndInsert(RadixTrieNode parent, RadixTrieNode child, char firstChar, 
                                   int commonLength, String prefix, int index, RateDetails rateDetail) {
        String edgeLabel = child.getEdgeLabel();
        String remainingPrefix = prefix.substring(index);
        
        // Create intermediate node with common prefix
        String commonPrefix = edgeLabel.substring(0, commonLength);
        RadixTrieNode intermediateNode = new RadixTrieNode(commonPrefix);
        
        // Update child's edge label to remaining part
        String childRemainingLabel = edgeLabel.substring(commonLength);
        child.setEdgeLabel(childRemainingLabel);
        
        // Add old child to intermediate node
        if (!childRemainingLabel.isEmpty()) {
            intermediateNode.addChild(childRemainingLabel.charAt(0), child);
        }
        
        // Replace child in parent with intermediate node
        parent.addChild(firstChar, intermediateNode);
        
        // Continue insertion from intermediate node
        insertRecursive(intermediateNode, prefix, index + commonLength, rateDetail);
    }
    
    /**
     * Search for rate candidates using longest prefix matching
     * 
     * @param destinationNumber The destination phone number
     * @return List of rate candidates found
     */
    public List<RateCandidate> search(String destinationNumber) {
        if (destinationNumber == null || destinationNumber.isEmpty()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            // Try different destination prefix lengths (longest first)
            for (int destLen = Math.min(destinationNumber.length(), 10); destLen >= 1; destLen--) {
                String destinationPrefix = destinationNumber.substring(0, destLen);
                
                RadixTrieNode node = searchExact(root, destinationPrefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    // Found matching node, create candidates
                    for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                        RateCandidate candidate = RateCandidate.create(
                                rateDetail, null, destinationPrefix, false);
                        candidates.add(candidate);
                    }
                }
            }
            
            log.debug("Found {} candidates for destination '{}'", candidates.size(), destinationNumber);
            return candidates;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Search for exact prefix match in the radix trie
     */
    private RadixTrieNode searchExact(RadixTrieNode node, String prefix, int index) {
        if (index >= prefix.length()) {
            return node;
        }
        
        char firstChar = prefix.charAt(index);
        RadixTrieNode child = node.getChild(firstChar);
        
        if (child == null) {
            return null;
        }
        
        String edgeLabel = child.getEdgeLabel();
        String remainingPrefix = prefix.substring(index);
        
        if (remainingPrefix.startsWith(edgeLabel)) {
            // Full edge match, continue search
            return searchExact(child, prefix, index + edgeLabel.length());
        } else {
            // Partial or no match
            return null;
        }
    }
    
    /**
     * Perform longest prefix match search
     * 
     * @param destinationNumber The destination number to search
     * @return List of all matching candidates (all prefix lengths)
     */
    public List<RateCandidate> longestPrefixMatch(String destinationNumber) {
        if (destinationNumber == null || destinationNumber.isEmpty()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> allCandidates = new ArrayList<>();
            searchLongestPrefixRecursive(root, destinationNumber, 0, "", allCandidates);
            
            log.debug("Found {} LPM candidates for destination '{}'", 
                    allCandidates.size(), destinationNumber);
            
            return allCandidates;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursive method for longest prefix matching in radix trie
     */
    private void searchLongestPrefixRecursive(RadixTrieNode node, String phoneNumber, int index, 
                                            String currentPrefix, List<RateCandidate> candidates) {
        // If this node is end of prefix, add its rate details as candidates
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                RateCandidate candidate = RateCandidate.create(
                        rateDetail, null, currentPrefix, false);
                candidates.add(candidate);
            }
        }
        
        // If we've reached the end of the phone number, stop
        if (index >= phoneNumber.length()) {
            return;
        }
        
        // Continue searching in children
        char ch = phoneNumber.charAt(index);
        RadixTrieNode child = node.getChild(ch);
        
        if (child != null) {
            String edgeLabel = child.getEdgeLabel();
            String remainingPhone = phoneNumber.substring(index);
            
            // Check how much of the edge label matches the remaining phone number
            int matchLength = RadixTrieNode.findCommonPrefixLength(edgeLabel, remainingPhone);
            
            if (matchLength > 0) {
                // Partial or full match, continue search
                String newCurrentPrefix = currentPrefix + edgeLabel.substring(0, matchLength);
                searchLongestPrefixRecursive(child, phoneNumber, index + matchLength, 
                        newCurrentPrefix, candidates);
            }
        }
    }
    
    /**
     * Clear all data from the radix trie
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            root.getChildren().clear();
            root.clearRateDetails();
            root.setEndOfPrefix(false);
            root.setPrefix(null);
            log.info("Destination-only radix trie cleared");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Get the total number of rate details stored in the radix trie
     * 
     * @return Total count of rate details
     */
    public int getTotalRateDetailsCount() {
        lock.readLock().lock();
        try {
            return countRateDetailsRecursive(root);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively count rate details in the radix trie
     */
    private int countRateDetailsRecursive(RadixTrieNode node) {
        int count = node.getRateDetailsCount();
        
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countRateDetailsRecursive(child);
        }
        
        return count;
    }
    
    /**
     * Get statistics about the radix trie
     * 
     * @return String containing trie statistics
     */
    public String getStatistics() {
        lock.readLock().lock();
        try {
            int totalNodes = countNodesRecursive(root);
            int totalRateDetails = getTotalRateDetailsCount();
            int endOfPrefixNodes = countEndOfPrefixNodes(root);
            
            return String.format("RadixDestinationOnlyTrie Stats: %d nodes, %d end-of-prefix nodes, %d rate details", 
                    totalNodes, endOfPrefixNodes, totalRateDetails);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively count total nodes in the radix trie
     */
    private int countNodesRecursive(RadixTrieNode node) {
        int count = 1; // Count this node
        
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countNodesRecursive(child);
        }
        
        return count;
    }
    
    /**
     * Recursively count end-of-prefix nodes in the radix trie
     */
    private int countEndOfPrefixNodes(RadixTrieNode node) {
        int count = node.isEndOfPrefix() ? 1 : 0;
        
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countEndOfPrefixNodes(child);
        }
        
        return count;
    }
}
