package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Rate Package specific RadixTrie for source-destination based rate matching.
 * Each instance is dedicated to a single rate package for optimal isolation and performance.
 * 
 * Key Features:
 * - Isolated per rate package for better cache locality
 * - Source-destination prefix combination matching
 * - CORRECTED: Proper longest prefix matching (root to leaf direction)
 * - Thread-safe operations
 * 
 * Trie Structure: sourcePrefix|destinationPrefix -> RateDetails
 * 
 * Time Complexity:
 * - Insert: O(k) where k = len(sourcePrefix) + len(destinationPrefix)
 * - Search: O(s * d * k) where s = source prefix variations, d = dest prefix variations, k = key length
 * - IMPROVED: Now follows proper trie traversal order
 * 
 * <AUTHOR> Developer
 */
@Slf4j
public class RatePackageSourceDestinationTrie extends AbstractRadixTrie {
    
    private static final String KEY_SEPARATOR = "|";
    // Removed hardcoded limit - using dynamic greedy approach instead
    
    /**
     * Constructor for rate package specific source-destination trie
     * 
     * @param ratePackageId The rate package ID this trie belongs to
     */
    public RatePackageSourceDestinationTrie(Long ratePackageId) {
        super(ratePackageId);
    }
    
    /**
     * Generate key for source-destination combination
     * Format: sourcePrefix|destinationPrefix
     * 
     * @param rateDetail The rate detail containing source and destination prefixes
     * @return Combined key string
     */
    @Override
    protected String generateKey(RateDetails rateDetail) {
        if (rateDetail.getSourcePrefix() == null || rateDetail.getDestinationPrefix() == null) {
            return null;
        }
        return rateDetail.getSourcePrefix() + KEY_SEPARATOR + rateDetail.getDestinationPrefix();
    }
    
    /**
     * Validate rate detail for source-destination trie
     * Must have both source and destination prefixes and belong to this rate package
     * 
     * @param rateDetail The rate detail to validate
     * @return true if valid for this trie type
     */
    @Override
    protected boolean isValidRateDetail(RateDetails rateDetail) {
        return rateDetail != null 
            && rateDetail.getSourcePrefix() != null 
            && !rateDetail.getSourcePrefix().trim().isEmpty()
            && rateDetail.getDestinationPrefix() != null 
            && !rateDetail.getDestinationPrefix().trim().isEmpty()
            && rateDetail.getRatePackage() != null
            && rateDetail.getRatePackage().getRatePackageId().equals(this.ratePackageId);
    }
    
    /**
     * Create rate candidate from rate detail and matched prefix
     * 
     * @param rateDetail The rate detail
     * @param matchedPrefix The matched combined prefix (source|destination)
     * @return Rate candidate
     */
    @Override
    protected RateCandidate createCandidate(RateDetails rateDetail, String matchedPrefix) {
        String[] parts = matchedPrefix.split("\\" + KEY_SEPARATOR);
        String sourcePrefix = parts.length > 0 ? parts[0] : null;
        String destinationPrefix = parts.length > 1 ? parts[1] : null;
        
        return RateCandidate.create(rateDetail, sourcePrefix, destinationPrefix, true);
    }
    
    /**
     * Get maximum prefix length for source-destination combinations
     */
    @Override
    protected int getMaxPrefixLength() {
        return MAX_PREFIX_LENGTH * 2 + KEY_SEPARATOR.length(); // source + separator + destination
    }
    
    /**
     * CORRECTED: Search for rate candidates using proper longest prefix matching algorithm.
     * Now follows correct trie traversal from root to leaf.
     * 
     * Algorithm:
     * 1. For each source prefix length (1 to max)
     * 2. For each destination prefix length (1 to max)
     * 3. Search in trie using proper root-to-leaf traversal
     * 4. Return all matching candidates for best-match selection
     * 
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @return List of rate candidates found, ordered by match quality
     */
    @Override
    public List<RateCandidate> search(String sourceNumber, String destinationNumber) {
        if (sourceNumber == null || destinationNumber == null) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            // CORRECTED: Start from shortest prefixes and go to longest (proper trie traversal)
            int maxSourceLen = Math.min(sourceNumber.length(), MAX_PREFIX_LENGTH);
            int maxDestLen = Math.min(destinationNumber.length(), MAX_PREFIX_LENGTH);
            
            for (int sourceLen = 1; sourceLen <= maxSourceLen; sourceLen++) {
                String sourcePrefix = sourceNumber.substring(0, sourceLen);
                
                for (int destLen = 1; destLen <= maxDestLen; destLen++) {
                    String destinationPrefix = destinationNumber.substring(0, destLen);
                    String combinedKey = sourcePrefix + KEY_SEPARATOR + destinationPrefix;
                    
                    RadixTrieNode node = searchExact(root, combinedKey, 0);
                    if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                        // Found matching node, create candidates
                        for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                            RateCandidate candidate = RateCandidate.create(
                                    rateDetail, sourcePrefix, destinationPrefix, true);
                            candidates.add(candidate);
                        }
                        
                        log.debug("Found {} candidates for source '{}', destination '{}' in package {}", 
                                node.getRateDetailsCount(), sourcePrefix, destinationPrefix, ratePackageId);
                    }
                }
            }
            
            log.debug("Total {} source-destination candidates found for package {}", 
                    candidates.size(), ratePackageId);
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get all unique source prefixes stored in this trie.
     * Useful for analytics and debugging.
     * 
     * @return List of unique source prefixes
     */
    public List<String> getAllSourcePrefixes() {
        lock.readLock().lock();
        try {
            List<String> sourcePrefixes = new ArrayList<>();
            collectSourcePrefixes(root, sourcePrefixes);
            return sourcePrefixes;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively collect all source prefixes from the trie
     */
    private void collectSourcePrefixes(RadixTrieNode node, List<String> sourcePrefixes) {
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String fullKey = node.getPrefix();
            if (fullKey != null && fullKey.contains(KEY_SEPARATOR)) {
                String sourcePrefix = fullKey.split("\\" + KEY_SEPARATOR)[0];
                if (!sourcePrefixes.contains(sourcePrefix)) {
                    sourcePrefixes.add(sourcePrefix);
                }
            }
        }
        
        for (RadixTrieNode child : node.getChildren().values()) {
            collectSourcePrefixes(child, sourcePrefixes);
        }
    }
    
    /**
     * Search for candidates with specific source and destination prefix lengths.
     * Useful for testing and debugging specific scenarios.
     * 
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @param sourceLength Specific source prefix length to try
     * @param destinationLength Specific destination prefix length to try
     * @return List of rate candidates found
     */
    public List<RateCandidate> searchWithSpecificLengths(String sourceNumber, String destinationNumber, 
                                                        int sourceLength, int destinationLength) {
        if (sourceNumber == null || destinationNumber == null 
            || sourceLength <= 0 || destinationLength <= 0
            || sourceLength > sourceNumber.length() || destinationLength > destinationNumber.length()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            String sourcePrefix = sourceNumber.substring(0, sourceLength);
            String destinationPrefix = destinationNumber.substring(0, destinationLength);
            String combinedKey = sourcePrefix + KEY_SEPARATOR + destinationPrefix;
            
            RadixTrieNode node = searchExact(root, combinedKey, 0);
            if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                    RateCandidate candidate = RateCandidate.create(
                            rateDetail, sourcePrefix, destinationPrefix, true);
                    candidates.add(candidate);
                }
            }
            
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get enhanced statistics including source-destination specific metrics
     */
    @Override
    public String getStatistics() {
        lock.readLock().lock();
        try {
            String baseStats = super.getStatistics();
            int uniqueSourcePrefixes = getAllSourcePrefixes().size();
            
            return baseStats + String.format(", %d unique source prefixes", uniqueSourcePrefixes);
        } finally {
            lock.readLock().unlock();
        }
    }
}
