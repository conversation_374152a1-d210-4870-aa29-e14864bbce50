package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Rate Package specific RadixTrie for source-destination based rate matching.
 * Each instance is dedicated to a single rate package for optimal isolation and performance.
 * 
 * Key Features:
 * - Isolated per rate package for better cache locality
 * - Source-destination prefix combination matching
 * - CORRECTED: Proper longest prefix matching (root to leaf direction)
 * - Thread-safe operations
 * 
 * Trie Structure: sourcePrefix|destinationPrefix -> RateDetails
 * 
 * Time Complexity:
 * - Insert: O(k) where k = len(sourcePrefix) + len(destinationPrefix)
 * - Search: O(s * d * k) where s = source prefix variations, d = dest prefix variations, k = key length
 * - IMPROVED: Now follows proper trie traversal order
 * 
 * <AUTHOR> Developer
 */
@Slf4j
public class RatePackageSourceDestinationTrie extends AbstractRadixTrie {
    
    private static final String KEY_SEPARATOR = "|";
    // Removed hardcoded limit - using dynamic greedy approach instead
    
    /**
     * Constructor for rate package specific source-destination trie
     * 
     * @param ratePackageId The rate package ID this trie belongs to
     */
    public RatePackageSourceDestinationTrie(Long ratePackageId) {
        super(ratePackageId);
    }
    
    /**
     * Generate key for source-destination combination
     * Format: sourcePrefix|destinationPrefix
     * 
     * @param rateDetail The rate detail containing source and destination prefixes
     * @return Combined key string
     */
    @Override
    protected String generateKey(RateDetails rateDetail) {
        if (rateDetail.getSourcePrefix() == null || rateDetail.getDestinationPrefix() == null) {
            return null;
        }
        return rateDetail.getSourcePrefix() + KEY_SEPARATOR + rateDetail.getDestinationPrefix();
    }
    
    /**
     * Validate rate detail for source-destination trie
     * Must have both source and destination prefixes and belong to this rate package
     * 
     * @param rateDetail The rate detail to validate
     * @return true if valid for this trie type
     */
    @Override
    protected boolean isValidRateDetail(RateDetails rateDetail) {
        return rateDetail != null 
            && rateDetail.getSourcePrefix() != null 
            && !rateDetail.getSourcePrefix().trim().isEmpty()
            && rateDetail.getDestinationPrefix() != null 
            && !rateDetail.getDestinationPrefix().trim().isEmpty()
            && rateDetail.getRatePackage() != null
            && rateDetail.getRatePackage().getRatePackageId().equals(this.ratePackageId);
    }
    
    /**
     * Create rate candidate from rate detail and matched prefix
     * 
     * @param rateDetail The rate detail
     * @param matchedPrefix The matched combined prefix (source|destination)
     * @return Rate candidate
     */
    @Override
    protected RateCandidate createCandidate(RateDetails rateDetail, String matchedPrefix) {
        String[] parts = matchedPrefix.split("\\" + KEY_SEPARATOR);
        String sourcePrefix = parts.length > 0 ? parts[0] : null;
        String destinationPrefix = parts.length > 1 ? parts[1] : null;
        
        return RateCandidate.create(rateDetail, sourcePrefix, destinationPrefix, true);
    }
    
    /**
     * OPTIMAL GREEDY: Get maximum prefix length dynamically based on actual data
     * This uses the longest combined key actually stored in the trie
     */
    @Override
    protected int getMaxPrefixLength() {
        return findLongestCombinedKeyInTrie();
    }

    /**
     * GREEDY OPTIMIZATION: Find the actual longest combined key stored in this trie
     * This ensures we don't miss any longer combinations that exist in the data
     */
    private int findLongestCombinedKeyInTrie() {
        lock.readLock().lock();
        try {
            return findLongestKeyRecursive(root, 0);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Recursively find the longest combined key in the trie
     */
    private int findLongestKeyRecursive(RadixTrieNode node, int currentDepth) {
        int maxLength = currentDepth;

        // If this node has rate details, it represents a valid combined key
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String combinedKey = node.getPrefix();
            if (combinedKey != null) {
                maxLength = Math.max(maxLength, combinedKey.length());
            }
        }

        // Recursively check all children
        for (RadixTrieNode child : node.getChildren().values()) {
            int childDepth = currentDepth + (child.getEdgeLabel() != null ? child.getEdgeLabel().length() : 0);
            int childMaxLength = findLongestKeyRecursive(child, childDepth);
            maxLength = Math.max(maxLength, childMaxLength);
        }

        return maxLength;
    }

    /**
     * GREEDY HELPER: Get maximum individual prefix lengths from actual data
     * Returns [maxSourceLength, maxDestinationLength] based on stored data
     */
    public int[] getMaxIndividualPrefixLengths() {
        lock.readLock().lock();
        try {
            int maxSourceLen = 0;
            int maxDestLen = 0;

            List<String> allKeys = getAllCombinedKeys();
            for (String combinedKey : allKeys) {
                if (combinedKey.contains(KEY_SEPARATOR)) {
                    String[] parts = combinedKey.split("\\" + KEY_SEPARATOR);
                    if (parts.length >= 2) {
                        maxSourceLen = Math.max(maxSourceLen, parts[0].length());
                        maxDestLen = Math.max(maxDestLen, parts[1].length());
                    }
                }
            }

            return new int[]{maxSourceLen, maxDestLen};
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Get all combined keys stored in the trie
     */
    private List<String> getAllCombinedKeys() {
        List<String> keys = new ArrayList<>();
        collectAllKeys(root, keys);
        return keys;
    }

    /**
     * Recursively collect all keys from the trie
     */
    private void collectAllKeys(RadixTrieNode node, List<String> keys) {
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String key = node.getPrefix();
            if (key != null) {
                keys.add(key);
            }
        }

        for (RadixTrieNode child : node.getChildren().values()) {
            collectAllKeys(child, keys);
        }
    }
    
    /**
     * OPTIMAL GREEDY: Search using dynamic longest prefix matching algorithm.
     * Uses actual trie data to determine optimal search ranges, not hardcoded limits.
     *
     * Greedy Algorithm:
     * 1. Dynamically determine max prefix lengths from actual trie data
     * 2. Search from shortest to longest prefixes (proper trie traversal)
     * 3. Use actual number lengths and trie data to optimize search space
     * 4. Return all matching candidates for best-match selection
     *
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @return List of rate candidates found, ordered by match quality
     */
    @Override
    public List<RateCandidate> search(String sourceNumber, String destinationNumber) {
        if (sourceNumber == null || sourceNumber.isEmpty() ||
            destinationNumber == null || destinationNumber.isEmpty()) {
            return new ArrayList<>();
        }

        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();

            // GREEDY OPTIMIZATION: Get actual max lengths from trie data
            int[] maxLengths = getMaxIndividualPrefixLengths();
            int maxSourceLen = Math.min(sourceNumber.length(), maxLengths[0]);
            int maxDestLen = Math.min(destinationNumber.length(), maxLengths[1]);

            // If trie is empty, no point in searching
            if (maxSourceLen <= 0 || maxDestLen <= 0) {
                log.debug("No valid prefixes found in trie for package {}, skipping search", ratePackageId);
                return candidates;
            }

            log.debug("GREEDY SEARCH: Searching source '{}' (max len {}) + destination '{}' (max len {}) for package {}",
                    sourceNumber, maxSourceLen, destinationNumber, maxDestLen, ratePackageId);

            // TRULY GREEDY: Search from longest to shortest prefixes (most specific first)
            // This finds the most specific (longest) matches first, which is optimal for telecom routing
            for (int sourceLen = maxSourceLen; sourceLen >= 1; sourceLen--) {
                String sourcePrefix = sourceNumber.substring(0, sourceLen);

                for (int destLen = maxDestLen; destLen >= 1; destLen--) {
                    String destinationPrefix = destinationNumber.substring(0, destLen);
                    String combinedKey = sourcePrefix + KEY_SEPARATOR + destinationPrefix;

                    RadixTrieNode node = searchExact(root, combinedKey, 0);
                    if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                        // Found matching node, create candidates
                        for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                            RateCandidate candidate = RateCandidate.create(
                                    rateDetail, sourcePrefix, destinationPrefix, true);
                            candidates.add(candidate);
                        }

                        log.debug("GREEDY MATCH: Found {} candidates for source '{}' (len={}) + destination '{}' (len={}) in package {}",
                                node.getRateDetailsCount(), sourcePrefix, sourceLen, destinationPrefix, destLen, ratePackageId);
                    }
                }
            }

            log.debug("GREEDY RESULT: Total {} source-destination candidates found for package {} (searched up to {},{} lengths)",
                    candidates.size(), ratePackageId, maxSourceLen, maxDestLen);
            return candidates;

        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get all unique source prefixes stored in this trie.
     * Useful for analytics and debugging.
     * 
     * @return List of unique source prefixes
     */
    public List<String> getAllSourcePrefixes() {
        lock.readLock().lock();
        try {
            List<String> sourcePrefixes = new ArrayList<>();
            collectSourcePrefixes(root, sourcePrefixes);
            return sourcePrefixes;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively collect all source prefixes from the trie
     */
    private void collectSourcePrefixes(RadixTrieNode node, List<String> sourcePrefixes) {
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String fullKey = node.getPrefix();
            if (fullKey != null && fullKey.contains(KEY_SEPARATOR)) {
                String sourcePrefix = fullKey.split("\\" + KEY_SEPARATOR)[0];
                if (!sourcePrefixes.contains(sourcePrefix)) {
                    sourcePrefixes.add(sourcePrefix);
                }
            }
        }
        
        for (RadixTrieNode child : node.getChildren().values()) {
            collectSourcePrefixes(child, sourcePrefixes);
        }
    }
    
    /**
     * Search for candidates with specific source and destination prefix lengths.
     * Useful for testing and debugging specific scenarios.
     * 
     * @param sourceNumber The source phone number
     * @param destinationNumber The destination phone number
     * @param sourceLength Specific source prefix length to try
     * @param destinationLength Specific destination prefix length to try
     * @return List of rate candidates found
     */
    public List<RateCandidate> searchWithSpecificLengths(String sourceNumber, String destinationNumber, 
                                                        int sourceLength, int destinationLength) {
        if (sourceNumber == null || destinationNumber == null 
            || sourceLength <= 0 || destinationLength <= 0
            || sourceLength > sourceNumber.length() || destinationLength > destinationNumber.length()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            String sourcePrefix = sourceNumber.substring(0, sourceLength);
            String destinationPrefix = destinationNumber.substring(0, destinationLength);
            String combinedKey = sourcePrefix + KEY_SEPARATOR + destinationPrefix;
            
            RadixTrieNode node = searchExact(root, combinedKey, 0);
            if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                    RateCandidate candidate = RateCandidate.create(
                            rateDetail, sourcePrefix, destinationPrefix, true);
                    candidates.add(candidate);
                }
            }
            
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get enhanced statistics including source-destination specific metrics
     */
    @Override
    public String getStatistics() {
        lock.readLock().lock();
        try {
            String baseStats = super.getStatistics();
            int uniqueSourcePrefixes = getAllSourcePrefixes().size();
            
            return baseStats + String.format(", %d unique source prefixes", uniqueSourcePrefixes);
        } finally {
            lock.readLock().unlock();
        }
    }
}
