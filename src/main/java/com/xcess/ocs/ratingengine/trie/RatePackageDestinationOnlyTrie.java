package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Rate Package specific RadixTrie for destination-only based rate matching.
 * Each instance is dedicated to a single rate package for optimal isolation and performance.
 * 
 * Key Features:
 * - Isolated per rate package for better cache locality
 * - Destination-only prefix matching (source prefix is null)
 * - CORRECTED: Proper longest prefix matching (root to leaf direction)
 * - Thread-safe operations
 * 
 * Trie Structure: destinationPrefix -> RateDetails
 * 
 * Time Complexity:
 * - Insert: O(k) where k = len(destinationPrefix)
 * - Search: O(d * k) where d = destination prefix variations, k = key length
 * - IMPROVED: Now follows proper trie traversal order
 * 
 * <AUTHOR> Developer
 */
@Slf4j
public class RatePackageDestinationOnlyTrie extends AbstractRadixTrie {
    
    // Removed hardcoded limit - using dynamic greedy approach instead
    
    /**
     * Constructor for rate package specific destination-only trie
     * 
     * @param ratePackageId The rate package ID this trie belongs to
     */
    public RatePackageDestinationOnlyTrie(Long ratePackageId) {
        super(ratePackageId);
    }
    
    /**
     * Generate key for destination-only matching
     * Format: destinationPrefix (source prefix is ignored)
     * 
     * @param rateDetail The rate detail containing destination prefix
     * @return Destination prefix as key
     */
    @Override
    protected String generateKey(RateDetails rateDetail) {
        return rateDetail.getDestinationPrefix();
    }
    
    /**
     * Validate rate detail for destination-only trie
     * Must have destination prefix, source prefix should be null, and belong to this rate package
     * 
     * @param rateDetail The rate detail to validate
     * @return true if valid for this trie type
     */
    @Override
    protected boolean isValidRateDetail(RateDetails rateDetail) {
        return rateDetail != null 
            && rateDetail.getDestinationPrefix() != null 
            && !rateDetail.getDestinationPrefix().trim().isEmpty()
            && rateDetail.getSourcePrefix() == null // Source prefix must be null for destination-only
            && rateDetail.getRatePackage() != null
            && rateDetail.getRatePackage().getRatePackageId().equals(this.ratePackageId);
    }
    
    /**
     * Create rate candidate from rate detail and matched prefix
     * 
     * @param rateDetail The rate detail
     * @param matchedPrefix The matched destination prefix
     * @return Rate candidate
     */
    @Override
    protected RateCandidate createCandidate(RateDetails rateDetail, String matchedPrefix) {
        return RateCandidate.create(rateDetail, null, matchedPrefix, false);
    }
    
    /**
     * Get maximum prefix length for destination-only matching
     */
    @Override
    protected int getMaxPrefixLength() {
        return MAX_PREFIX_LENGTH;
    }
    
    /**
     * CORRECTED: Search for rate candidates using proper longest prefix matching algorithm.
     * Now follows correct trie traversal from root to leaf.
     * 
     * Algorithm:
     * 1. For each destination prefix length (1 to max) - CORRECTED ORDER
     * 2. Search in trie using proper root-to-leaf traversal
     * 3. Return all matching candidates for best-match selection
     * 
     * @param sourceNumber The source phone number (ignored for destination-only matching)
     * @param destinationNumber The destination phone number
     * @return List of rate candidates found, ordered by match quality
     */
    @Override
    public List<RateCandidate> search(String sourceNumber, String destinationNumber) {
        if (destinationNumber == null) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            // CORRECTED: Start from shortest prefix (1) and go to longest (proper trie traversal)
            int maxDestLen = Math.min(destinationNumber.length(), MAX_PREFIX_LENGTH);
            
            for (int destLen = 1; destLen <= maxDestLen; destLen++) {
                String destinationPrefix = destinationNumber.substring(0, destLen);
                
                RadixTrieNode node = searchExact(root, destinationPrefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    // Found matching node, create candidates
                    for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                        RateCandidate candidate = RateCandidate.create(
                                rateDetail, null, destinationPrefix, false);
                        candidates.add(candidate);
                    }
                    
                    log.debug("Found {} candidates for destination '{}' in package {}", 
                            node.getRateDetailsCount(), destinationPrefix, ratePackageId);
                }
            }
            
            log.debug("Total {} destination-only candidates found for package {}", 
                    candidates.size(), ratePackageId);
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get all unique destination prefixes stored in this trie.
     * Useful for analytics and debugging.
     * 
     * @return List of unique destination prefixes
     */
    public List<String> getAllDestinationPrefixes() {
        lock.readLock().lock();
        try {
            List<String> destinationPrefixes = new ArrayList<>();
            collectDestinationPrefixes(root, destinationPrefixes);
            return destinationPrefixes;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively collect all destination prefixes from the trie
     */
    private void collectDestinationPrefixes(RadixTrieNode node, List<String> destinationPrefixes) {
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String prefix = node.getPrefix();
            if (prefix != null && !destinationPrefixes.contains(prefix)) {
                destinationPrefixes.add(prefix);
            }
        }
        
        for (RadixTrieNode child : node.getChildren().values()) {
            collectDestinationPrefixes(child, destinationPrefixes);
        }
    }
    
    /**
     * Search for candidates with specific destination prefix length.
     * Useful for testing and debugging specific scenarios.
     * 
     * @param destinationNumber The destination phone number
     * @param destinationLength Specific destination prefix length to try
     * @return List of rate candidates found
     */
    public List<RateCandidate> searchWithSpecificLength(String destinationNumber, int destinationLength) {
        if (destinationNumber == null || destinationLength <= 0 
            || destinationLength > destinationNumber.length()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            String destinationPrefix = destinationNumber.substring(0, destinationLength);
            
            RadixTrieNode node = searchExact(root, destinationPrefix, 0);
            if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                    RateCandidate candidate = RateCandidate.create(
                            rateDetail, null, destinationPrefix, false);
                    candidates.add(candidate);
                }
            }
            
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Find the longest matching destination prefix for a given number.
     * Returns the actual prefix that would be matched, useful for debugging.
     * 
     * @param destinationNumber The destination phone number
     * @return The longest matching prefix, or null if no match
     */
    public String findLongestMatchingPrefix(String destinationNumber) {
        if (destinationNumber == null) {
            return null;
        }
        
        lock.readLock().lock();
        try {
            // CORRECTED: Search from shortest to longest to find the longest match
            String longestMatch = null;
            int maxDestLen = Math.min(destinationNumber.length(), MAX_PREFIX_LENGTH);
            
            for (int destLen = 1; destLen <= maxDestLen; destLen++) {
                String destinationPrefix = destinationNumber.substring(0, destLen);
                
                RadixTrieNode node = searchExact(root, destinationPrefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    longestMatch = destinationPrefix; // Keep updating to get the longest
                }
            }
            
            return longestMatch;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Check if a specific destination prefix exists in the trie
     * 
     * @param destinationPrefix The destination prefix to check
     * @return true if the prefix exists and has rate details
     */
    public boolean hasDestinationPrefix(String destinationPrefix) {
        if (destinationPrefix == null) {
            return false;
        }
        
        lock.readLock().lock();
        try {
            RadixTrieNode node = searchExact(root, destinationPrefix, 0);
            return node != null && node.isEndOfPrefix() && node.hasRateDetails();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get enhanced statistics including destination-specific metrics
     */
    @Override
    public String getStatistics() {
        lock.readLock().lock();
        try {
            String baseStats = super.getStatistics();
            int uniqueDestinationPrefixes = getAllDestinationPrefixes().size();
            
            return baseStats + String.format(", %d unique destination prefixes", uniqueDestinationPrefixes);
        } finally {
            lock.readLock().unlock();
        }
    }
}
