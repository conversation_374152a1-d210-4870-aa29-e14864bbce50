package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Rate Package specific RadixTrie for destination-only based rate matching.
 * Each instance is dedicated to a single rate package for optimal isolation and performance.
 * 
 * Key Features:
 * - Isolated per rate package for better cache locality
 * - Destination-only prefix matching (source prefix is null)
 * - CORRECTED: Proper longest prefix matching (root to leaf direction)
 * - Thread-safe operations
 * 
 * Trie Structure: destinationPrefix -> RateDetails
 * 
 * Time Complexity:
 * - Insert: O(k) where k = len(destinationPrefix)
 * - Search: O(d * k) where d = destination prefix variations, k = key length
 * - IMPROVED: Now follows proper trie traversal order
 * 
 * <AUTHOR> Developer
 */
@Slf4j
public class RatePackageDestinationOnlyTrie extends AbstractRadixTrie {
    
    // Removed hardcoded limit - using dynamic greedy approach instead
    
    /**
     * Constructor for rate package specific destination-only trie
     * 
     * @param ratePackageId The rate package ID this trie belongs to
     */
    public RatePackageDestinationOnlyTrie(Long ratePackageId) {
        super(ratePackageId);
    }
    
    /**
     * Generate key for destination-only matching
     * Format: destinationPrefix (source prefix is ignored)
     * 
     * @param rateDetail The rate detail containing destination prefix
     * @return Destination prefix as key
     */
    @Override
    protected String generateKey(RateDetails rateDetail) {
        return rateDetail.getDestinationPrefix();
    }
    
    /**
     * Validate rate detail for destination-only trie
     * Must have destination prefix, source prefix should be null, and belong to this rate package
     * 
     * @param rateDetail The rate detail to validate
     * @return true if valid for this trie type
     */
    @Override
    protected boolean isValidRateDetail(RateDetails rateDetail) {
        return rateDetail != null 
            && rateDetail.getDestinationPrefix() != null 
            && !rateDetail.getDestinationPrefix().trim().isEmpty()
            && rateDetail.getSourcePrefix() == null // Source prefix must be null for destination-only
            && rateDetail.getRatePackage() != null
            && rateDetail.getRatePackage().getRatePackageId().equals(this.ratePackageId);
    }
    
    /**
     * Create rate candidate from rate detail and matched prefix
     * 
     * @param rateDetail The rate detail
     * @param matchedPrefix The matched destination prefix
     * @return Rate candidate
     */
    @Override
    protected RateCandidate createCandidate(RateDetails rateDetail, String matchedPrefix) {
        return RateCandidate.create(rateDetail, null, matchedPrefix, false);
    }
    
    /**
     * OPTIMAL GREEDY: Get maximum prefix length dynamically based on actual data
     * This uses the longest prefix actually stored in the trie, not a hardcoded limit
     */
    @Override
    protected int getMaxPrefixLength() {
        return findLongestPrefixInTrie();
    }

    /**
     * GREEDY OPTIMIZATION: Find the actual longest prefix stored in this trie
     * This ensures we don't miss any longer prefixes that exist in the data
     */
    private int findLongestPrefixInTrie() {
        lock.readLock().lock();
        try {
            return findLongestPrefixRecursive(root, 0);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Recursively find the longest prefix in the trie
     */
    private int findLongestPrefixRecursive(RadixTrieNode node, int currentDepth) {
        int maxLength = currentDepth;

        // If this node has rate details, it represents a valid prefix
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String prefix = node.getPrefix();
            if (prefix != null) {
                maxLength = Math.max(maxLength, prefix.length());
            }
        }

        // Recursively check all children
        for (RadixTrieNode child : node.getChildren().values()) {
            int childDepth = currentDepth + (child.getEdgeLabel() != null ? child.getEdgeLabel().length() : 0);
            int childMaxLength = findLongestPrefixRecursive(child, childDepth);
            maxLength = Math.max(maxLength, childMaxLength);
        }

        return maxLength;
    }
    
    /**
     * OPTIMAL GREEDY: Search using dynamic longest prefix matching algorithm.
     * Uses actual trie data to determine optimal search range, not hardcoded limits.
     *
     * Greedy Algorithm:
     * 1. Dynamically determine max prefix length from actual trie data
     * 2. Search from shortest to longest prefix (proper trie traversal)
     * 3. Continue until we've exhausted all possible prefixes in the number
     * 4. Return all matching candidates for best-match selection
     *
     * @param sourceNumber The source phone number (ignored for destination-only matching)
     * @param destinationNumber The destination phone number
     * @return List of rate candidates found, ordered by match quality
     */
    @Override
    public List<RateCandidate> search(String sourceNumber, String destinationNumber) {
        if (destinationNumber == null || destinationNumber.isEmpty()) {
            return new ArrayList<>();
        }

        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();

            // GREEDY OPTIMIZATION: Use actual number length and trie data to determine search range
            int maxDestLen = Math.min(destinationNumber.length(), getMaxPrefixLength());

            // If trie is empty, no point in searching
            if (maxDestLen <= 0) {
                log.debug("No prefixes found in trie for package {}, skipping search", ratePackageId);
                return candidates;
            }

            log.debug("GREEDY SEARCH: Searching destination '{}' with dynamic max length {} for package {}",
                    destinationNumber, maxDestLen, ratePackageId);

            // TRULY GREEDY: Search from longest to shortest prefix (longest match first)
            // This is the correct greedy approach - find the most specific match first
            for (int destLen = maxDestLen; destLen >= 1; destLen--) {
                String destinationPrefix = destinationNumber.substring(0, destLen);

                RadixTrieNode node = searchExact(root, destinationPrefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    // Found matching node, create candidates
                    for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                        RateCandidate candidate = RateCandidate.create(
                                rateDetail, null, destinationPrefix, false);
                        candidates.add(candidate);
                    }

                    log.debug("GREEDY MATCH: Found {} candidates for destination '{}' (len={}) in package {}",
                            node.getRateDetailsCount(), destinationPrefix, destLen, ratePackageId);
                }
            }

            log.debug("GREEDY RESULT: Total {} destination-only candidates found for package {} (searched up to length {})",
                    candidates.size(), ratePackageId, maxDestLen);
            return candidates;

        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get all unique destination prefixes stored in this trie.
     * Useful for analytics and debugging.
     * 
     * @return List of unique destination prefixes
     */
    public List<String> getAllDestinationPrefixes() {
        lock.readLock().lock();
        try {
            List<String> destinationPrefixes = new ArrayList<>();
            collectDestinationPrefixes(root, destinationPrefixes);
            return destinationPrefixes;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively collect all destination prefixes from the trie
     */
    private void collectDestinationPrefixes(RadixTrieNode node, List<String> destinationPrefixes) {
        if (node.isEndOfPrefix() && node.hasRateDetails()) {
            String prefix = node.getPrefix();
            if (prefix != null && !destinationPrefixes.contains(prefix)) {
                destinationPrefixes.add(prefix);
            }
        }
        
        for (RadixTrieNode child : node.getChildren().values()) {
            collectDestinationPrefixes(child, destinationPrefixes);
        }
    }
    
    /**
     * Search for candidates with specific destination prefix length.
     * Useful for testing and debugging specific scenarios.
     * 
     * @param destinationNumber The destination phone number
     * @param destinationLength Specific destination prefix length to try
     * @return List of rate candidates found
     */
    public List<RateCandidate> searchWithSpecificLength(String destinationNumber, int destinationLength) {
        if (destinationNumber == null || destinationLength <= 0 
            || destinationLength > destinationNumber.length()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            String destinationPrefix = destinationNumber.substring(0, destinationLength);
            
            RadixTrieNode node = searchExact(root, destinationPrefix, 0);
            if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                    RateCandidate candidate = RateCandidate.create(
                            rateDetail, null, destinationPrefix, false);
                    candidates.add(candidate);
                }
            }
            
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * OPTIMAL GREEDY: Find the longest matching destination prefix for a given number.
     * Uses dynamic search range based on actual trie data.
     *
     * @param destinationNumber The destination phone number
     * @return The longest matching prefix, or null if no match
     */
    public String findLongestMatchingPrefix(String destinationNumber) {
        if (destinationNumber == null || destinationNumber.isEmpty()) {
            return null;
        }

        lock.readLock().lock();
        try {
            // GREEDY: Use dynamic max length based on actual trie data
            String longestMatch = null;
            int maxDestLen = Math.min(destinationNumber.length(), getMaxPrefixLength());

            log.debug("GREEDY LONGEST MATCH: Searching '{}' up to length {} for package {}",
                    destinationNumber, maxDestLen, ratePackageId);

            // TRULY GREEDY: Search from longest to shortest to find the longest match FIRST
            for (int destLen = maxDestLen; destLen >= 1; destLen--) {
                String destinationPrefix = destinationNumber.substring(0, destLen);

                RadixTrieNode node = searchExact(root, destinationPrefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    longestMatch = destinationPrefix; // First match is the longest due to our search order
                    log.debug("GREEDY FOUND: Longest match '{}' (len={}) for package {}",
                            destinationPrefix, destLen, ratePackageId);
                    break; // GREEDY: Stop at first (longest) match found
                }
            }

            log.debug("GREEDY FINAL: Longest match for '{}' is '{}' in package {}",
                    destinationNumber, longestMatch, ratePackageId);
            return longestMatch;

        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Check if a specific destination prefix exists in the trie
     * 
     * @param destinationPrefix The destination prefix to check
     * @return true if the prefix exists and has rate details
     */
    public boolean hasDestinationPrefix(String destinationPrefix) {
        if (destinationPrefix == null) {
            return false;
        }
        
        lock.readLock().lock();
        try {
            RadixTrieNode node = searchExact(root, destinationPrefix, 0);
            return node != null && node.isEndOfPrefix() && node.hasRateDetails();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Get enhanced statistics including destination-specific metrics
     */
    @Override
    public String getStatistics() {
        lock.readLock().lock();
        try {
            String baseStats = super.getStatistics();
            int uniqueDestinationPrefixes = getAllDestinationPrefixes().size();
            
            return baseStats + String.format(", %d unique destination prefixes", uniqueDestinationPrefixes);
        } finally {
            lock.readLock().unlock();
        }
    }
}
