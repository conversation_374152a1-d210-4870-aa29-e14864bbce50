package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails; // Assuming RateDetails might be used by node, but not directly by abstract methods here.
import lombok.extern.slf4j.Slf4j;

import java.util.List; // Added for search method signature placeholder
import java.util.Map; // Keep for getChildren() in RadixTrieNode if used by logic here
import java.util.concurrent.locks.ReentrantReadWriteLock;
import com.xcess.ocs.ratingengine.model.RateCandidate; // Added for search method signature placeholder

@Slf4j
public abstract class AbstractRadixTrie {

    protected final RadixTrieNode root;
    protected final ReentrantReadWriteLock lock;

    public AbstractRadixTrie() {
        this.root = new RadixTrieNode(); // Root node with empty edge label
        this.lock = new ReentrantReadWriteLock();
    }

    /**
     * Recursive method to insert a rate detail into the radix trie.
     * This is the core insertion logic shared by concrete trie implementations.
     *
     * @param node Current node in traversal.
     * @param keyToInsert The full key (prefix) being inserted.
     * @param index Current index in keyToInsert.
     * @param rateDetail The rate detail to associate with this key.
     */
    protected void insertRecursive(RadixTrieNode node, String keyToInsert, int index, RateDetails rateDetail) {
        if (index >= keyToInsert.length()) {
            node.markAsEndOfPrefix(keyToInsert);
            node.addRateDetail(rateDetail);
            return;
        }

        char firstChar = keyToInsert.charAt(index);
        RadixTrieNode child = node.getChild(firstChar);

        if (child == null) {
            // No child exists, create new node with remaining key as edge label
            String remainingKey = keyToInsert.substring(index);
            RadixTrieNode newChild = new RadixTrieNode(remainingKey, keyToInsert);
            newChild.markAsEndOfPrefix(keyToInsert); // Mark the new child as end of this specific prefix
            newChild.addRateDetail(rateDetail);
            node.addChild(firstChar, newChild);
            return;
        }

        // Child exists, check edge label match
        String edgeLabel = child.getEdgeLabel();
        String remainingKeyToInsert = keyToInsert.substring(index);
        int commonLength = RadixTrieNode.findCommonPrefixLength(edgeLabel, remainingKeyToInsert);

        if (commonLength == edgeLabel.length()) {
            // Full edge match with the child's current edgeLabel,
            // so continue recursively into the child with the rest of the keyToInsert.
            insertRecursive(child, keyToInsert, index + commonLength, rateDetail);
        } else if (commonLength > 0) {
            // Partial edge match, need to split the existing child node.
            splitNodeAndInsert(node, child, firstChar, commonLength, keyToInsert, index, rateDetail);
        } else {
            // No common prefix (commonLength = 0). This is an unexpected state if child was fetched by firstChar
            // and both edgeLabel and remainingKeyToInsert are non-empty.
            // It implies the first character of edgeLabel does not match the first character of remainingKeyToInsert,
            // which contradicts how 'child' was retrieved via node.getChild(firstChar).
            // This case needs careful review if hit, potentially indicating an issue in RadixTrieNode.findCommonPrefixLength
            // or an edge case like an empty edgeLabel on a child (which shouldn't typically happen for established children).
        }
    }

    /**
     * Splits an existing child node when a new key partially matches its edge label.
     *
     * @param parent The parent of the child being split.
     * @param existingChild The child node that needs to be split.
     * @param firstCharOfEdge The first character of the edge leading to existingChild (and intermediateNode).
     * @param commonPrefixLength The length of the common prefix between existingChild's edgeLabel and the keyBeingInserted.
     * @param keyToInsert The full key (prefix) that is being inserted.
     * @param indexInKey The current starting index in keyToInsert corresponding to the beginning of the common prefix part.
     * @param rateDetail The rate detail to associate with the keyToInsert.
     */
    protected void splitNodeAndInsert(RadixTrieNode parent, RadixTrieNode existingChild, char firstCharOfEdge, 
                                   int commonPrefixLength, String keyToInsert, int indexInKey, RateDetails rateDetail) {
        String existingEdgeLabel = existingChild.getEdgeLabel();
        
        // 1. Create the new intermediate node. Its edge label is the common part.
        String commonEdgePart = existingEdgeLabel.substring(0, commonPrefixLength);
        RadixTrieNode intermediateNode = new RadixTrieNode(commonEdgePart);
        // Intermediate node itself is not an endOfPrefix unless the keyToInsert happens to end here.

        // 2. Update the existing child: its edge label becomes the part *after* the common prefix.
        String existingChildNewLabel = existingEdgeLabel.substring(commonPrefixLength);
        existingChild.setEdgeLabel(existingChildNewLabel);

        // 3. Add the (now modified) existingChild as a child of the intermediateNode.
        // The key for this is the first character of the existingChild's new label.
        if (!existingChildNewLabel.isEmpty()) {
            intermediateNode.addChild(existingChildNewLabel.charAt(0), existingChild);
        } else {
            // This implies the existingChild's entire edgeLabel was the commonPrefixLength.
            // So, the intermediateNode now fully represents what existingChild represented before its edge was split.
            // If existingChild was an endOfPrefix, these properties (isEndOfPrefix, prefix, rateDetails) should conceptually move to intermediateNode.
            // However, the key being inserted might also terminate at intermediateNode or extend further.
            // This specific case needs careful handling to ensure rateDetails are not lost or incorrectly assigned.
            // If existingChildNewLabel is empty, it means existingChild represented the prefix that is now intermediateNode.
            // All of existingChild's properties (isEndOfPrefix, prefix string, rateDetails) should be transferred to intermediateNode.
            // Then, existingChild effectively becomes redundant or might be repurposed if the new key branches off it.
            // For simplicity in radix, often an empty new label implies the original child node becomes a child of intermediate with an empty edge, 
            // or its data is merged if the new key also terminates at intermediateNode.

            // Let's assume if existingChildNewLabel is empty, intermediateNode takes over existingChild's endOfPrefix status and details.
            if(existingChild.isEndOfPrefix()) {
                intermediateNode.markAsEndOfPrefix(existingChild.getPrefix()); // Use original prefix of existingChild
                intermediateNode.addRateDetails(existingChild.getRateDetailsUnmodifiable()); // Copy details
                // Now, existingChild itself should not be an end of this prefix anymore, nor hold these details if they moved.
                // This makes existingChild a simple node, possibly with its own children but no data/prefix mark itself.
                // We are not clearing existingChild's rate details here as it might still have children and be part of other paths if it wasn't an endofPrefix.
                // The key is: what was associated with the *original prefix* of existingChild now belongs to intermediateNode.
            }
             // existingChild itself (the object) is still added, but its role changes. 
            // If its new label is empty, it effectively means the split occurred AT existingChild.
            // It should retain its own children. The intermediate node becomes its new parent for the commonEdgePart.
            // This is complex. A simpler model: if existingChildNewLabel is empty, intermediateNode gets existingChild's *isEndOfPrefix* and *RateDetails*. 
            // existingChild itself might just have children now under intermediateNode if its old edge label matched commonEdgePart exactly.
            // The original structure of `addChild` might need existingChild to have a non-empty label if it is to be a child.
            // This scenario (empty existingChildNewLabel) means intermediateNode *is* what existingChild used to be (for that prefix length).
            // The `existingChild` object would then be re-added as a child of `intermediateNode` based on the *next character* of its *original path* if it had further children.
            // This part of Radix split is tricky. Reverting to a simpler model: existingChild is re-parented.
            // If existingChildNewLabel is empty, this intermediateNode has now fully absorbed the prefix that existingChild represented by its original edgeLabel. 
            // The existingChild (the object) should now hang off the intermediateNode if it has further children, keyed by the next char after the common prefix.
            // However, the addChild below uses existingChildNewLabel.charAt(0) which would fail if empty.
            // Correct handling: if existingChildNewLabel is empty, intermediateNode effectively becomes the node that existingChild was. 
            // The existingChild's children are now children of intermediateNode.
            // And then we continue inserting the *new* key from intermediateNode.
            // This means copying children from existingChild to intermediateNode if existingChildNewLabel is empty.
            if(existingChild.isEndOfPrefix()) { // This logic has to be precise
                 intermediateNode.markAsEndOfPrefix(existingChild.getPrefix());
                 intermediateNode.addRateDetails(existingChild.getRateDetailsUnmodifiable());
                 // The existingChild is effectively replaced by intermediateNode for this prefix.
                 // existingChild's own children should become intermediateNode's children.
                 for(Map.Entry<Character, RadixTrieNode> entry : existingChild.getChildren().entrySet()){
                     intermediateNode.addChild(entry.getKey(), entry.getValue());
                 }
                 // existingChild is no longer directly attached to parent via firstCharOfEdge for this path.
                 // The intermediateNode takes that slot. The original existingChild object might be orphaned or GC'd if not re-added.
                 // This simplified model seems problematic. The more standard way:
                 // existingChild *is* re-added under intermediateNode. If its new label is empty, it means it *was* the common prefix.
                 // In this case, intermediateNode absorbs its properties. existingChild is only re-added if it *still* has a remaining path/label.
                 // The current `intermediateNode.addChild(existingChildNewLabel.charAt(0), existingChild);` handles the case where existingChildNewLabel is not empty.
                 // If it IS empty, existingChild has no further unique path segment from intermediateNode based on its old edgeLabel.
                 // It means intermediateNode *is* the node that used to be existingChild (for that prefix). 
                 // So, if keyToInsert also ends here, details go to intermediateNode. If keyToInsert continues, it branches from intermediateNode.
                 // And the *original* children of existingChild should now belong to intermediateNode.
                 // Let's stick to the case where existingChildNewLabel IS NOT empty for adding as a direct child, otherwise merge properties and children.
            }
            // If existingChildNewLabel is empty, it means intermediateNode now *is* the node that existingChild represented.
            // So, transfer properties and children from existingChild to intermediateNode.
            if (existingChildNewLabel.isEmpty()) {
                if (existingChild.isEndOfPrefix()) {
                    intermediateNode.markAsEndOfPrefix(existingChild.getPrefix());
                    intermediateNode.addRateDetails(existingChild.getRateDetailsUnmodifiable());
                }
                // Transfer children from existingChild to intermediateNode
                for (Map.Entry<Character, RadixTrieNode> entry : existingChild.getChildren().entrySet()) {
                    intermediateNode.addChild(entry.getKey(), entry.getValue());
                }
                // existingChild itself is not added as a child of intermediateNode in this case because intermediateNode *is* it now.
            } else {
                 intermediateNode.addChild(existingChildNewLabel.charAt(0), existingChild);
            }
        }

        // 4. Replace the original existingChild in the parent with the new intermediateNode.
        parent.addChild(firstCharOfEdge, intermediateNode);

        // 5. Continue inserting the rest of the keyToInsert.
        //    The keyToInsert might terminate at intermediateNode, or it might require a new branch from intermediateNode.
        String remainingPartOfKeyToInsert = keyToInsert.substring(indexInKey + commonPrefixLength);

        if (remainingPartOfKeyToInsert.isEmpty()) {
            // The keyToInsert ends exactly at the intermediate node (it matched commonEdgePart).
            intermediateNode.markAsEndOfPrefix(keyToInsert); // keyToInsert is the full prefix for this rateDetail
            intermediateNode.addRateDetail(rateDetail);
        } else {
            // The keyToInsert has a further part. This forms a new branch from intermediateNode.
            // Create a new child for this remaining part.
            RadixTrieNode newChildForRemainder = new RadixTrieNode(remainingPartOfKeyToInsert, keyToInsert);
            newChildForRemainder.markAsEndOfPrefix(keyToInsert);
            newChildForRemainder.addRateDetail(rateDetail);
            intermediateNode.addChild(remainingPartOfKeyToInsert.charAt(0), newChildForRemainder);
        }
    }

    /**
     * Performs the core logic of clearing the trie.
     * Subclasses should call this and then perform any specific logging.
     */
    protected void performClear() {
        // The root itself is not removed, but its children and data are cleared.
        if (root != null) {
            root.getChildren().clear();
            root.clearRateDetails(); // Clear any details potentially stored at root (though unlikely for prefix tries)
            root.setEndOfPrefix(false); // Root itself isn't an end of a prefix in this context
            root.setPrefix(null); // Root doesn't represent a prefix
            // Edge label for root is typically empty or null, set by RadixTrieNode constructor
        }
    }

    /**
     * Get the total number of rate details stored in the radix trie.
     *
     * @return Total count of rate details.
     */
    public int getTotalRateDetailsCount() {
        lock.readLock().lock();
        try {
            return countRateDetailsRecursive(root);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Recursively count rate details in the radix trie starting from the given node.
     *
     * @param node The node to start counting from.
     * @return The total count of rate details in the subtree rooted at this node.
     */
    protected int countRateDetailsRecursive(RadixTrieNode node) {
        if (node == null) {
            return 0;
        }
        int count = node.getRateDetailsCount();
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countRateDetailsRecursive(child);
        }
        return count;
    }

    /**
     * Recursively count all nodes in the trie starting from the given node.
     *
     * @param node The node to start counting from.
     * @return The total number of nodes in the subtree rooted at this node.
     */
    protected int countNodesRecursive(RadixTrieNode node) {
        if (node == null) {
            return 0;
        }
        int count = 1; // Count current node
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countNodesRecursive(child);
        }
        return count;
    }

    /**
     * Recursively count nodes that are marked as end of a prefix.
     *
     * @param node The node to start counting from.
     * @return The total number of end-of-prefix nodes in the subtree.
     */
    protected int countEndOfPrefixNodes(RadixTrieNode node) {
        if (node == null) {
            return 0;
        }
        int count = node.isEndOfPrefix() ? 1 : 0;
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countEndOfPrefixNodes(child);
        }
        return count;
    }

    // Abstract methods to be implemented by subclasses for specific functionalities
    // For example, insert and search methods are highly specific to the key structure.
    public abstract void insert(RateDetails rateDetail);
    // Example search signature, concrete classes will define their own specific search methods
    // public abstract List<RateCandidate> search(String... params);
    public abstract void clear();
    public abstract String getStatistics();
} 