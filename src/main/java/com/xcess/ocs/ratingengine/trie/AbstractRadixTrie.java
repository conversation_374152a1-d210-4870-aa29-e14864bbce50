package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Abstract base class for RadixTrie implementations following OOP principles.
 * Eliminates code duplication and provides common functionality.
 * 
 * Key OOP Concepts Applied:
 * - Template Method Pattern: Common algorithms with specialized behavior
 * - Strategy Pattern: Different key generation strategies
 * - Single Responsibility: Each method has one clear purpose
 * - Open/Closed Principle: Open for extension, closed for modification
 * 
 * Time Complexity:
 * - Insert: O(k) where k is the key length
 * - Search: O(k * m) where k is key length, m is number of prefix lengths
 * - Space: O(n * k) where n is number of entries, k is average key length
 * 
 * <AUTHOR> Developer
 */
@Slf4j
public abstract class AbstractRadixTrie {
    
    protected final RadixTrieNode root;
    protected final ReentrantReadWriteLock lock;
    protected final Long ratePackageId;
    
    /**
     * Constructor for rate package specific trie
     * 
     * @param ratePackageId The rate package ID this trie belongs to
     */
    protected AbstractRadixTrie(Long ratePackageId) {
        this.root = new RadixTrieNode();
        this.lock = new ReentrantReadWriteLock();
        this.ratePackageId = ratePackageId;
        log.debug("Created {} for rate package: {}", getClass().getSimpleName(), ratePackageId);
    }
    
    /**
     * Insert a rate detail into the trie.
     * Template method - subclasses implement specific validation and key generation.
     * 
     * @param rateDetail The rate detail to insert
     */
    public final void insert(RateDetails rateDetail) {
        if (!isValidRateDetail(rateDetail)) {
            log.warn("Invalid rate detail for insertion in {}: {}", getClass().getSimpleName(), rateDetail);
            return;
        }
        
        String key = generateKey(rateDetail);
        if (key == null || key.isEmpty()) {
            log.warn("Cannot generate key for rate detail in {}: {}", getClass().getSimpleName(), rateDetail);
            return;
        }
        
        lock.writeLock().lock();
        try {
            insertRecursive(root, key, 0, rateDetail);
            log.debug("Inserted rate detail with key: {} for rate package: {}", key, ratePackageId);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Recursive insertion method for radix trie
     * Common algorithm used by all trie implementations
     */
    protected final void insertRecursive(RadixTrieNode node, String key, int index, RateDetails rateDetail) {
        if (index >= key.length()) {
            node.markAsEndOfPrefix(key);
            node.addRateDetail(rateDetail);
            return;
        }
        
        char firstChar = key.charAt(index);
        RadixTrieNode child = node.getChild(firstChar);
        
        if (child == null) {
            // No child exists, create new node with remaining key as edge label
            String remainingKey = key.substring(index);
            RadixTrieNode newChild = new RadixTrieNode(remainingKey, key);
            newChild.markAsEndOfPrefix(key);
            newChild.addRateDetail(rateDetail);
            node.addChild(firstChar, newChild);
            return;
        }
        
        // Child exists, check edge label match
        String edgeLabel = child.getEdgeLabel();
        String remainingKey = key.substring(index);
        int commonLength = RadixTrieNode.findCommonPrefixLength(edgeLabel, remainingKey);
        
        if (commonLength == edgeLabel.length()) {
            // Full edge match, continue recursively
            insertRecursive(child, key, index + commonLength, rateDetail);
        } else if (commonLength > 0) {
            // Partial edge match, need to split the node
            splitNodeAndInsert(node, child, firstChar, commonLength, key, index, rateDetail);
        }
    }
    
    /**
     * Split a node when there's a partial edge match
     * Common algorithm used by all trie implementations
     */
    protected final void splitNodeAndInsert(RadixTrieNode parent, RadixTrieNode child, char firstChar, 
                                          int commonLength, String key, int index, RateDetails rateDetail) {
        String edgeLabel = child.getEdgeLabel();
        String remainingKey = key.substring(index);
        
        // Create intermediate node with common prefix
        String commonPrefix = edgeLabel.substring(0, commonLength);
        RadixTrieNode intermediateNode = new RadixTrieNode(commonPrefix);
        
        // Update child's edge label to remaining part
        String childRemainingLabel = edgeLabel.substring(commonLength);
        child.setEdgeLabel(childRemainingLabel);
        
        // Add old child to intermediate node
        if (!childRemainingLabel.isEmpty()) {
            intermediateNode.addChild(childRemainingLabel.charAt(0), child);
        }
        
        // Replace child in parent with intermediate node
        parent.addChild(firstChar, intermediateNode);
        
        // Continue insertion from intermediate node
        insertRecursive(intermediateNode, key, index + commonLength, rateDetail);
    }
    
    /**
     * CORRECTED: Longest prefix matching with proper direction (root to leaf)
     * This fixes the issue where search was going from longest to shortest incorrectly.
     * 
     * @param number The phone number to search
     * @return List of rate candidates found
     */
    protected final List<RateCandidate> longestPrefixMatch(String number) {
        if (number == null || number.isEmpty()) {
            return new ArrayList<>();
        }
        
        lock.readLock().lock();
        try {
            List<RateCandidate> candidates = new ArrayList<>();
            
            // CORRECTED: Start from shortest prefix (1) and go to longest (number.length())
            // This follows the natural trie traversal from root to leaf
            for (int prefixLen = 1; prefixLen <= Math.min(number.length(), getMaxPrefixLength()); prefixLen++) {
                String prefix = number.substring(0, prefixLen);
                
                RadixTrieNode node = searchExact(root, prefix, 0);
                if (node != null && node.isEndOfPrefix() && node.hasRateDetails()) {
                    // Found matching node, create candidates
                    for (RateDetails rateDetail : node.getRateDetailsUnmodifiable()) {
                        RateCandidate candidate = createCandidate(rateDetail, prefix);
                        candidates.add(candidate);
                    }
                }
            }
            
            log.debug("Found {} candidates using corrected longest prefix match for package {}", 
                    candidates.size(), ratePackageId);
            return candidates;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Search for exact key match in the radix trie
     * Common algorithm used by all trie implementations
     */
    protected final RadixTrieNode searchExact(RadixTrieNode node, String key, int index) {
        if (index >= key.length()) {
            return node;
        }
        
        char firstChar = key.charAt(index);
        RadixTrieNode child = node.getChild(firstChar);
        
        if (child == null) {
            return null;
        }
        
        String edgeLabel = child.getEdgeLabel();
        String remainingKey = key.substring(index);
        
        if (remainingKey.startsWith(edgeLabel)) {
            return searchExact(child, key, index + edgeLabel.length());
        } else {
            return null;
        }
    }
    
    /**
     * Clear all data from the trie
     */
    public final void clear() {
        lock.writeLock().lock();
        try {
            root.getChildren().clear();
            root.clearRateDetails();
            root.setEndOfPrefix(false);
            root.setPrefix(null);
            log.info("Cleared {} for rate package: {}", getClass().getSimpleName(), ratePackageId);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Get the total number of rate details stored in the trie
     */
    public final int getTotalRateDetailsCount() {
        lock.readLock().lock();
        try {
            return countRateDetailsRecursive(root);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Recursively count rate details in the trie
     */
    protected final int countRateDetailsRecursive(RadixTrieNode node) {
        int count = node.getRateDetailsCount();
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countRateDetailsRecursive(child);
        }
        return count;
    }
    
    /**
     * Get rate package ID this trie belongs to
     */
    public final Long getRatePackageId() {
        return ratePackageId;
    }
    
    /**
     * Get statistics about the trie
     */
    public String getStatistics() {
        lock.readLock().lock();
        try {
            int totalNodes = countNodesRecursive(root);
            int totalRateDetails = getTotalRateDetailsCount();
            int endOfPrefixNodes = countEndOfPrefixNodes(root);
            
            return String.format("%s (Package %d): %d nodes, %d end-of-prefix nodes, %d rate details", 
                    getClass().getSimpleName(), ratePackageId, totalNodes, endOfPrefixNodes, totalRateDetails);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    protected final int countNodesRecursive(RadixTrieNode node) {
        int count = 1;
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countNodesRecursive(child);
        }
        return count;
    }
    
    protected final int countEndOfPrefixNodes(RadixTrieNode node) {
        int count = node.isEndOfPrefix() ? 1 : 0;
        for (RadixTrieNode child : node.getChildren().values()) {
            count += countEndOfPrefixNodes(child);
        }
        return count;
    }
    
    // Abstract methods for subclass-specific behavior
    
    /**
     * Generate the key for storing rate detail in trie
     */
    protected abstract String generateKey(RateDetails rateDetail);
    
    /**
     * Validate if rate detail is suitable for this trie type
     */
    protected abstract boolean isValidRateDetail(RateDetails rateDetail);
    
    /**
     * Create rate candidate from rate detail and matched prefix
     */
    protected abstract RateCandidate createCandidate(RateDetails rateDetail, String matchedPrefix);
    
    /**
     * Get maximum prefix length for this trie type
     */
    protected abstract int getMaxPrefixLength();
    
    /**
     * Search for rate candidates (subclass-specific implementation)
     */
    public abstract List<RateCandidate> search(String sourceNumber, String destinationNumber);
}
