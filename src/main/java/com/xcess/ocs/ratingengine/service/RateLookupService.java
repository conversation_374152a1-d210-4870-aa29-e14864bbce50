package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import com.xcess.ocs.ratingengine.trie.RadixDestinationOnlyTrie;
import com.xcess.ocs.ratingengine.trie.RadixSourceDestinationTrie;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * Main service for CPaaS best-match routing algorithm.
 * Implements the two-phase lookup strategy using Radix Trie-based data structures.
 *
 * Phase 1: Source-Destination matching using RadixSourceDestinationTrie
 * Phase 2: Destination-only matching using RadixDestinationOnlyTrie (fallback)
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLookupService {

    private final RadixSourceDestinationTrie sourceDestinationTrie;
    private final RadixDestinationOnlyTrie destinationOnlyTrie;
    
    /**
     * Find the best rate for a call using the two-phase lookup strategy.
     *
     * Phase 1: Source-Destination matching (preferred)
     * Phase 2: Destination-only matching (fallback)
     *
     * @param callSourceNumber The source phone number
     * @param callDestinationNumber The destination phone number
     * @param callTimestamp The timestamp of the call
     * @return The best matching RateDetail, or null if no match found
     */
    public RateDetails findBestRate(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        if (callSourceNumber == null || callDestinationNumber == null || callTimestamp == null) {
            log.warn("Invalid input parameters: source={}, destination={}, timestamp={}",
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }

        log.debug("Finding best rate for call: source={}, destination={}, timestamp={}",
                callSourceNumber, callDestinationNumber, callTimestamp);

        // Phase 1: Source-Destination Match
        List<RateCandidate> phase1Candidates = sourceDestinationTrie.search(callSourceNumber, callDestinationNumber);
        List<RateCandidate> validPhase1Candidates = filterValidCandidates(phase1Candidates, callTimestamp);

        if (!validPhase1Candidates.isEmpty()) {
            log.debug("Found {} valid Phase 1 candidates", validPhase1Candidates.size());
            RateCandidate bestCandidate = selectBestCandidate(validPhase1Candidates);
            if (bestCandidate != null) {
                log.info("Best rate found in Phase 1: rateDetailId={}, sourceMatch={}, destMatch={}",
                        bestCandidate.getRateDetail().getRateDetailsId(),
                        bestCandidate.getMatchedSourcePrefix(),
                        bestCandidate.getMatchedDestinationPrefix());
                return bestCandidate.getRateDetail();
            }
        }

        // Phase 2: Destination-Only Match (Fallback)
        log.debug("No valid Phase 1 candidates found, proceeding to Phase 2");
        List<RateCandidate> phase2Candidates = destinationOnlyTrie.longestPrefixMatch(callDestinationNumber);
        List<RateCandidate> validPhase2Candidates = filterValidCandidates(phase2Candidates, callTimestamp);

        if (!validPhase2Candidates.isEmpty()) {
            log.debug("Found {} valid Phase 2 candidates", validPhase2Candidates.size());
            RateCandidate bestCandidate = selectBestCandidate(validPhase2Candidates);
            if (bestCandidate != null) {
                log.info("Best rate found in Phase 2: rateDetailId={}, destMatch={}",
                        bestCandidate.getRateDetail().getRateDetailsId(),
                        bestCandidate.getMatchedDestinationPrefix());
                return bestCandidate.getRateDetail();
            }
        }

        log.info("No matching rate found for call: source={}, destination={}, timestamp={}",
                callSourceNumber, callDestinationNumber, callTimestamp);
        return null;
    }
    
    /**
     * Filter candidates to include only those valid at the given timestamp
     *
     * @param candidates List of rate candidates
     * @param callTimestamp The call timestamp
     * @return List of valid candidates
     */
    private List<RateCandidate> filterValidCandidates(List<RateCandidate> candidates, LocalDateTime callTimestamp) {
        if (candidates == null || candidates.isEmpty()) {
            return new ArrayList<>();
        }

        List<RateCandidate> validCandidates = new ArrayList<>();
        for (RateCandidate candidate : candidates) {
            if (candidate.isValidAt(callTimestamp)) {
                validCandidates.add(candidate);
            } else {
                log.debug("Candidate filtered out - not valid at timestamp {}: {}", callTimestamp, candidate);
            }
        }

        return validCandidates;
    }

    /**
     * Select the best candidate from a list of valid candidates
     *
     * @param candidates List of valid candidates
     * @return The best candidate, or null if none found
     */
    private RateCandidate selectBestCandidate(List<RateCandidate> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }

        // Sort by match quality (RateCandidate implements Comparable)
        // Priority: Source-destination match > longer prefixes > rate package creation time
        return candidates.stream()
                .min(RateCandidate::compareTo)
                .orElse(null);
    }

    /**
     * Get statistics about the tries
     *
     * @return String containing trie statistics
     */
    public String getTrieStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Trie Statistics ===\n");
        stats.append(sourceDestinationTrie.getStatistics()).append("\n");
        stats.append(destinationOnlyTrie.getStatistics()).append("\n");
        return stats.toString();
    }

    /**
     * Clear all tries (useful for testing or reinitialization)
     */
    public void clearAllTries() {
        sourceDestinationTrie.clear();
        destinationOnlyTrie.clear();
        log.info("All tries cleared");
    }

    /**
     * Get detailed lookup information for debugging purposes
     *
     * @param callSourceNumber The source phone number
     * @param callDestinationNumber The destination phone number
     * @param callTimestamp The call timestamp
     * @return Detailed lookup information
     */
    public String getDetailedLookupInfo(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        StringBuilder info = new StringBuilder();
        info.append("=== Rate Lookup Details ===\n");
        info.append(String.format("Source: %s, Destination: %s, Timestamp: %s\n",
                callSourceNumber, callDestinationNumber, callTimestamp));

        // Phase 1 details
        List<RateCandidate> phase1Candidates = sourceDestinationTrie.search(callSourceNumber, callDestinationNumber);
        info.append(String.format("Phase 1 candidates found: %d\n", phase1Candidates.size()));
        phase1Candidates.forEach(c -> info.append(String.format("  - %s\n", c)));

        List<RateCandidate> validPhase1 = filterValidCandidates(phase1Candidates, callTimestamp);
        info.append(String.format("Valid Phase 1 candidates: %d\n", validPhase1.size()));

        // Phase 2 details
        List<RateCandidate> phase2Candidates = destinationOnlyTrie.longestPrefixMatch(callDestinationNumber);
        info.append(String.format("Phase 2 candidates found: %d\n", phase2Candidates.size()));
        phase2Candidates.forEach(c -> info.append(String.format("  - %s\n", c)));

        List<RateCandidate> validPhase2 = filterValidCandidates(phase2Candidates, callTimestamp);
        info.append(String.format("Valid Phase 2 candidates: %d\n", validPhase2.size()));

        // Best candidate selection
        RateCandidate bestCandidate = null;
        if (!validPhase1.isEmpty()) {
            bestCandidate = selectBestCandidate(validPhase1);
            info.append("Best candidate selected from Phase 1\n");
        } else if (!validPhase2.isEmpty()) {
            bestCandidate = selectBestCandidate(validPhase2);
            info.append("Best candidate selected from Phase 2\n");
        }

        if (bestCandidate != null) {
            info.append(String.format("Best rate: %s\n", bestCandidate));
        } else {
            info.append("No best rate found\n");
        }

        return info.toString();
    }
}
