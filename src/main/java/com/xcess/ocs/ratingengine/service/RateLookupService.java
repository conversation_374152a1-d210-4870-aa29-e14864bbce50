package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.cache.RatePackageCache;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.ratingengine.cache.RatePackageTries;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * IMPROVED: Enhanced Rate Lookup Service with per-package trie support.
 * Implements the two-phase lookup strategy across ALL rate packages.
 *
 * Key Improvements:
 * - Searches across all rate packages (not just global tries)
 * - Each rate package has isolated tries for better performance
 * - Proper OOP design with delegation to package-specific tries
 * - Corrected prefix matching algorithm (root to leaf)
 *
 * Algorithm Flow:
 * 1. Iterate through all rate packages
 * 2. For each package, search both source-destination and destination-only tries
 * 3. Collect all candidates from all packages
 * 4. Filter valid candidates based on timestamp
 * 5. Select best candidate using comparison logic
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLookupService {

    private final RatePackageCache ratePackageCache;
    
    /**
     * IMPROVED: Find the best rate across ALL rate packages using per-package tries.
     *
     * Algorithm:
     * 1. Search across all rate packages
     * 2. For each package, search both source-destination and destination-only tries
     * 3. Collect all candidates from all packages
     * 4. Filter valid candidates based on timestamp
     * 5. Select best candidate using comparison logic
     *
     * @param callSourceNumber The source phone number
     * @param callDestinationNumber The destination phone number
     * @param callTimestamp The timestamp of the call
     * @return The best matching RateDetail, or null if no match found
     */
    public RateDetails findBestRate(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        if (callSourceNumber == null || callDestinationNumber == null || callTimestamp == null) {
            log.warn("Invalid input parameters: source={}, destination={}, timestamp={}",
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }

        log.debug("Finding best rate across all packages for call: source={}, destination={}, timestamp={}",
                callSourceNumber, callDestinationNumber, callTimestamp);

        // Get all rate package tries
        Collection<RatePackageTries> allPackageTries = ratePackageCache.getAllRatePackageTries();
        if (allPackageTries.isEmpty()) {
            log.warn("No rate package tries found in cache. Cannot perform lookup.");
            return null;
        }

        List<RateCandidate> allCandidates = new ArrayList<>();

        // Search across all rate packages
        for (RatePackageTries packageTries : allPackageTries) {
            List<RateCandidate> packageCandidates = packageTries.searchAllCandidates(
                    callSourceNumber, callDestinationNumber);
            allCandidates.addAll(packageCandidates);

            log.debug("Package {} contributed {} candidates",
                    packageTries.getRatePackageId(), packageCandidates.size());
        }

        if (allCandidates.isEmpty()) {
            log.info("No rate candidates found across any rate package for call: source={}, destination={}, timestamp={}",
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }

        log.debug("Found {} total candidates across all packages before filtering", allCandidates.size());

        // Filter valid candidates
        List<RateCandidate> validCandidates = filterValidCandidates(allCandidates, callTimestamp);

        if (validCandidates.isEmpty()) {
            log.info("No valid candidates found after timestamp filtering for call: source={}, destination={}, timestamp={}",
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }

        // Select best candidate
        RateCandidate bestCandidate = selectBestCandidate(validCandidates);

        if (bestCandidate != null) {
            log.info("Best rate found: rateDetailId={}, sourceMatch={}, destMatch={}, packageId={}, isSDMatch={}",
                    bestCandidate.getRateDetail().getRateDetailsId(),
                    bestCandidate.getMatchedSourcePrefix(),
                    bestCandidate.getMatchedDestinationPrefix(),
                    bestCandidate.getRateDetail().getRatePackage() != null ?
                            bestCandidate.getRateDetail().getRatePackage().getRatePackageId() : "N/A",
                    bestCandidate.isSourceDestinationMatch());
            return bestCandidate.getRateDetail();
        }

        log.info("No best rate could be selected from valid candidates for call: source={}, destination={}, timestamp={}",
                callSourceNumber, callDestinationNumber, callTimestamp);
        return null;
    }
    
    /**
     * Filter candidates to include only those valid at the given timestamp
     *
     * @param candidates List of rate candidates
     * @param callTimestamp The call timestamp
     * @return List of valid candidates
     */
    private List<RateCandidate> filterValidCandidates(List<RateCandidate> candidates, LocalDateTime callTimestamp) {
        if (candidates == null || candidates.isEmpty()) {
            return new ArrayList<>();
        }

        List<RateCandidate> validCandidates = new ArrayList<>();
        for (RateCandidate candidate : candidates) {
            if (candidate.isValidAt(callTimestamp)) {
                validCandidates.add(candidate);
            } else {
                log.debug("Candidate filtered out - not valid at timestamp {}: {}", callTimestamp, candidate);
            }
        }

        return validCandidates;
    }

    /**
     * Select the best candidate from a list of valid candidates
     *
     * @param candidates List of valid candidates
     * @return The best candidate, or null if none found
     */
    private RateCandidate selectBestCandidate(List<RateCandidate> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }

        // Sort by match quality (RateCandidate implements Comparable)
        // Priority: Source-destination match > longer prefixes > rate package creation time
        return candidates.stream()
                .min(RateCandidate::compareTo)
                .orElse(null);
    }

    /**
     * IMPROVED: Get statistics about all package tries
     *
     * @return String containing comprehensive trie statistics
     */
    public String getTrieStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Per-Package Trie Statistics ===\n");

        Collection<RatePackageTries> allPackageTries = ratePackageCache.getAllRatePackageTries();
        if (allPackageTries.isEmpty()) {
            stats.append("No rate package tries found in cache.\n");
        } else {
            int totalPackages = allPackageTries.size();
            int totalRateDetails = allPackageTries.stream()
                    .mapToInt(RatePackageTries::getTotalRateDetailsCount)
                    .sum();

            stats.append(String.format("Total Packages: %d, Total Rate Details: %d\n",
                    totalPackages, totalRateDetails));
            stats.append("---\n");

            for (RatePackageTries packageTries : allPackageTries) {
                stats.append(packageTries.getStatistics()).append("\n");
            }
        }

        return stats.toString();
    }

    /**
     * IMPROVED: Clear all package tries (useful for testing or reinitialization)
     */
    public void clearAllTries() {
        Collection<RatePackageTries> allPackageTries = ratePackageCache.getAllRatePackageTries();
        for (RatePackageTries packageTries : allPackageTries) {
            packageTries.clear();
        }
        log.info("All package tries cleared");
    }

    /**
     * IMPROVED: Get detailed lookup information across all packages for debugging
     *
     * @param callSourceNumber The source phone number
     * @param callDestinationNumber The destination phone number
     * @param callTimestamp The call timestamp
     * @return Detailed lookup information
     */
    public String getDetailedLookupInfo(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        StringBuilder info = new StringBuilder();
        info.append("=== Enhanced Rate Lookup Details (All Packages) ===\n");
        info.append(String.format("Source: %s, Destination: %s, Timestamp: %s\n",
                callSourceNumber, callDestinationNumber, callTimestamp));

        Collection<RatePackageTries> allPackageTries = ratePackageCache.getAllRatePackageTries();
        if (allPackageTries.isEmpty()) {
            info.append("No rate package tries found in cache.\n");
            return info.toString();
        }

        List<RateCandidate> allCandidates = new ArrayList<>();
        int packageCounter = 1;

        // Search each package
        for (RatePackageTries packageTries : allPackageTries) {
            info.append(String.format("--- Package %d (ID: %d) ---\n",
                    packageCounter++, packageTries.getRatePackageId()));

            List<RateCandidate> packageCandidates = packageTries.searchAllCandidates(
                    callSourceNumber, callDestinationNumber);

            info.append(String.format("  Candidates found: %d\n", packageCandidates.size()));
            packageCandidates.forEach(c -> {
                info.append(String.format("    - %s\n", c));
                allCandidates.add(c);
            });
        }

        info.append("--- Overall Results ---\n");
        info.append(String.format("Total candidates before filtering: %d\n", allCandidates.size()));

        List<RateCandidate> validCandidates = filterValidCandidates(allCandidates, callTimestamp);
        info.append(String.format("Valid candidates after filtering: %d\n", validCandidates.size()));
        validCandidates.forEach(c -> info.append(String.format("  Valid: %s\n", c)));

        RateCandidate bestCandidate = selectBestCandidate(validCandidates);
        if (bestCandidate != null) {
            info.append(String.format("Best rate selected: %s\n", bestCandidate));
            info.append(String.format("  Package ID: %s\n",
                    bestCandidate.getRateDetail().getRatePackage() != null ?
                            bestCandidate.getRateDetail().getRatePackage().getRatePackageId() : "N/A"));
        } else {
            info.append("No best rate found\n");
        }

        return info.toString();
    }
}
