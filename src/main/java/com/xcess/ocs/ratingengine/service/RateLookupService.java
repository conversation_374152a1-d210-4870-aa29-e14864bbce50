package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.cache.RatePackageCache;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage; // For accessing createdAt
import com.xcess.ocs.ratingengine.cache.RatePackageTries;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Main service for CPaaS best-match routing algorithm.
 * Implements the two-phase lookup strategy by iterating through tries of all applicable rate packages.
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLookupService {
    
    private final RatePackageCache ratePackageCache; // Injected cache

    // Removed direct trie dependencies
    // private final RadixSourceDestinationTrie sourceDestinationTrie;
    // private final RadixDestinationOnlyTrie destinationOnlyTrie;
    
    /**
     * Find the best rate for a call by searching across ALL rate packages.
     * 
     * @param callSourceNumber The source phone number
     * @param callDestinationNumber The destination phone number  
     * @param callTimestamp The timestamp of the call
     * @return The best matching RateDetail, or null if no match found
     */
    public RateDetails findBestRate(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        if (callSourceNumber == null || callDestinationNumber == null || callTimestamp == null) {
            log.warn("Invalid input parameters: source={}, destination={}, timestamp={}", 
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }
        
        log.debug("Finding best rate across all packages for call: source={}, destination={}, timestamp={}", 
                callSourceNumber, callDestinationNumber, callTimestamp);
        
        Collection<RatePackageTries> allTries = ratePackageCache.getAllRatePackageTries();
        if (allTries.isEmpty()) {
            log.warn("No rate package tries found in cache. Cannot perform lookup.");
            return null;
        }

        List<RateCandidate> allPotentialCandidates = new ArrayList<>();

        for (RatePackageTries packageTries : allTries) {
            // It's important that RateDetails objects used to build these tries 
            // have a reference to their parent RatePackage entity for createdAt timestamp.
            // Assuming RateDetails has getRatePackage() which returns RatePackage entity.

            // Phase 1: Source-Destination Match within this package's SD trie
            List<RateCandidate> phase1PackageCandidates = packageTries.getSourceDestinationTrie().search(callSourceNumber, callDestinationNumber);
            allPotentialCandidates.addAll(phase1PackageCandidates);

            // Phase 2: Destination-Only Match within this package's D-Only trie
            // Note: RateLookupService previously only went to Phase 2 if Phase 1 had NO candidates.
            // Now, collecting all possible candidates from both phases from all packages first.
            List<RateCandidate> phase2PackageCandidates = packageTries.getDestinationOnlyTrie().longestPrefixMatch(callDestinationNumber);
            allPotentialCandidates.addAll(phase2PackageCandidates);
        }

        if (allPotentialCandidates.isEmpty()) {
            log.info("No rate candidates found across any rate package for call: source={}, destination={}, timestamp={}", 
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }

        log.debug("Found {} total potential candidates across all packages before filtering.", allPotentialCandidates.size());

        List<RateCandidate> validCandidates = filterAndPrepareCandidates(allPotentialCandidates, callTimestamp);
        
        if (validCandidates.isEmpty()) {
            log.info("No valid (time-applicable and with createdAt) rate candidates found after filtering for call: source={}, destination={}, timestamp={}", 
                    callSourceNumber, callDestinationNumber, callTimestamp);
            return null;
        }
        
        RateCandidate bestCandidate = selectBestCandidate(validCandidates);
        
        if (bestCandidate != null) {
            log.info("Best rate found: rateDetailId={}, sourceMatch={}, destMatch={}, packageId={}, isSDMatch={}", 
                    bestCandidate.getRateDetail().getRateDetailsId(),
                    bestCandidate.getMatchedSourcePrefix(),
                    bestCandidate.getMatchedDestinationPrefix(),
                    bestCandidate.getRateDetail().getRatePackage() != null ? bestCandidate.getRateDetail().getRatePackage().getRatePackageId() : "N/A",
                    bestCandidate.isSourceDestinationMatch());
            return bestCandidate.getRateDetail();
        }
        
        log.info("No best rate could be selected from valid candidates for call: source={}, destination={}, timestamp={}", 
                callSourceNumber, callDestinationNumber, callTimestamp);
        return null;
    }
    
    /**
     * Filters candidates to include only those valid at the given timestamp and 
     * attempts to populate ratePackageCreatedAt if not already set by trie search methods.
     * 
     * @param candidates List of rate candidates from trie lookups.
     * @param callTimestamp The call timestamp.
     * @return List of valid and prepared candidates.
     */
    private List<RateCandidate> filterAndPrepareCandidates(List<RateCandidate> candidates, LocalDateTime callTimestamp) {
        List<RateCandidate> validAndPrepared = new ArrayList<>();
        for (RateCandidate candidate : candidates) {
            if (candidate.isValidAt(callTimestamp)) {
                // Ensure ratePackageCreatedAt is populated for comparison
                // The trie search methods should now be populating this.
                // This is a fallback/verification if RatePackage entity structure requires it.
                if (candidate.getRatePackageCreatedAt() == null && 
                    candidate.getRateDetail() != null && 
                    candidate.getRateDetail().getRatePackage() != null) {
                    
                    RatePackage rp = candidate.getRateDetail().getRatePackage();
                    // Ensure RatePackage entity has getCreatedAt() method that returns LocalDateTime
                    // candidate.setRatePackageCreatedAt(rp.getCreatedAt()); 
                    // Log if you need to set it here, implies it wasn't set during trie candidate creation
                    log.trace("RatePackageCreatedAt was null for candidate of RateDetailId {}, attempting to set from RatePackageId {}.", 
                                candidate.getRateDetail().getRateDetailsId(), rp.getRatePackageId());
                     // This explicit set might not be needed if RateCandidate.create in tries is correctly modified 
                     // AND RatePackage has getCreatedAt(). For now, rely on trie population.
                }
                validAndPrepared.add(candidate);
            } else {
                log.debug("Candidate for RateDetailId {} is not valid at timestamp {}. Matched Src: '{}', Dest: '{}'", 
                        candidate.getRateDetail() != null ? candidate.getRateDetail().getRateDetailsId() : "N/A", 
                        callTimestamp, 
                        candidate.getMatchedSourcePrefix(), 
                        candidate.getMatchedDestinationPrefix());
            }
        }
        return validAndPrepared;
    }

    /**
     * Select the best candidate from a list of valid candidates based on RateCandidate.compareTo().
     */
    private RateCandidate selectBestCandidate(List<RateCandidate> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }
        
        // RateCandidate.compareTo implements the selection logic:
        // 1. Source-destination match preferred.
        // 2. Longer matched source prefix.
        // 3. Longer matched destination prefix.
        // 4. Tie-breaking by ratePackageCreatedAt (more recent is better).
        Optional<RateCandidate> bestCandidateOpt = candidates.stream().min(RateCandidate::compareTo);
        
        return bestCandidateOpt.orElse(null);
    }
    
    /**
     * Get detailed lookup information for debugging purposes (adapted for multi-package search).
     */
    public String getDetailedLookupInfo(String callSourceNumber, String callDestinationNumber, LocalDateTime callTimestamp) {
        StringBuilder info = new StringBuilder();
        info.append("=== Rate Lookup Details (All Packages) ===\n");
        info.append(String.format("Source: %s, Destination: %s, Timestamp: %s\n", 
                callSourceNumber, callDestinationNumber, callTimestamp));
        
        Collection<RatePackageTries> allTries = ratePackageCache.getAllRatePackageTries();
        if (allTries.isEmpty()) {
            info.append("No rate package tries found in cache.\n");
            return info.toString();
        }

        List<RateCandidate> allPotentialCandidatesDetails = new ArrayList<>();
        int packageCounter = 1;

        for (RatePackageTries packageTries : allTries) {
            // We'd need a way to get packageId if we want to log it here. 
            // For now, just an ordinal.
            info.append(String.format("--- Searching in Package Set %d ---\n", packageCounter++));

            List<RateCandidate> phase1PackageCandidates = packageTries.getSourceDestinationTrie().search(callSourceNumber, callDestinationNumber);
            info.append(String.format("  Phase 1 candidates found: %d\n", phase1PackageCandidates.size()));
            phase1PackageCandidates.forEach(c -> {
                allPotentialCandidatesDetails.add(c); // Collect for overall valid list
                info.append(String.format("    - %s\n", c));
            });

            List<RateCandidate> phase2PackageCandidates = packageTries.getDestinationOnlyTrie().longestPrefixMatch(callDestinationNumber);
            info.append(String.format("  Phase 2 candidates found: %d\n", phase2PackageCandidates.size()));
            phase2PackageCandidates.forEach(c -> {
                allPotentialCandidatesDetails.add(c); // Collect for overall valid list
                info.append(String.format("    - %s\n", c));
            });
        }

        info.append("--- Overall Filtering and Selection ---\n");
        info.append(String.format("Total potential candidates before filtering: %d\n", allPotentialCandidatesDetails.size()));
        
        List<RateCandidate> validCandidatesDetails = filterAndPrepareCandidates(allPotentialCandidatesDetails, callTimestamp);
        info.append(String.format("Total valid candidates after time filtering: %d\n", validCandidatesDetails.size()));
        validCandidatesDetails.forEach(c -> info.append(String.format("  - Valid: %s\n", c)));

        RateCandidate bestOverallCandidate = selectBestCandidate(validCandidatesDetails);
        if (bestOverallCandidate != null) {
            info.append(String.format("Best rate selected: DetailID=%d, RateValue=%.4f (PackageID %s)\n", 
                    bestOverallCandidate.getRateDetail().getRateDetailsId(), 
                    bestOverallCandidate.getRateDetail().getRate(), // Assuming getRate() exists
                    bestOverallCandidate.getRateDetail().getRatePackage() != null ? 
                        bestOverallCandidate.getRateDetail().getRatePackage().getRatePackageId() : "N/A"));
        } else {
            info.append("No best rate found overall.\n");
        }
        
        return info.toString();
    }
}
