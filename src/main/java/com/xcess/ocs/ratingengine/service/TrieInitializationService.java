package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.cache.RatePackageCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * IMPROVED: Service responsible for coordinating the initialization of per-package tries.
 * Now delegates to RatePackageCache which handles per-package trie initialization.
 *
 * Key Improvements:
 * - Simplified design with clear separation of concerns
 * - Delegates trie initialization to RatePackageCache
 * - Removes redundant code and global trie dependencies
 * - Follows Single Responsibility Principle
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrieInitializationService {

    private final RatePackageCache ratePackageCache;

    private volatile boolean isInitializationEnsured = false;
    
    /**
     * IMPROVED: Ensure cache initialization when application is ready.
     * RatePackageCache handles per-package trie initialization via @PostConstruct.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void ensureCacheInitializationOnStartup() {
        log.info("Application ready event received. Verifying per-package trie initialization...");

        if (ratePackageCache != null) {
            // RatePackageCache.preloadCache() is called via @PostConstruct
            // and handles per-package trie initialization
            log.info("RatePackageCache is expected to be initialized with per-package tries via @PostConstruct.");
            isInitializationEnsured = true;
        } else {
            log.error("RatePackageCache is not available. Per-package trie data may not be loaded.");
            isInitializationEnsured = false;
        }
    }

    /**
     * IMPROVED: Manually trigger a refresh of all per-package tries.
     *
     * @return CompletableFuture that completes when the cache refresh is initiated.
     */
    public CompletableFuture<Void> refreshTriesAndCache() {
        log.info("Request to refresh all per-package tries and rate package cache data...");

        if (ratePackageCache != null) {
            // RatePackageCache.refreshCache() handles per-package trie refresh
            ratePackageCache.refreshCache();
            log.info("Per-package trie and cache refresh initiated.");
            return CompletableFuture.completedFuture(null);
        } else {
            log.error("RatePackageCache not available for refresh.");
            return CompletableFuture.failedFuture(new IllegalStateException("RatePackageCache not available."));
        }
    }

    /**
     * IMPROVED: Check if the per-package tries are expected to be initialized.
     *
     * @return true if per-package tries are expected to be ready, false otherwise.
     */
    public boolean isEngineReady() {
        return isInitializationEnsured;
    }

    /**
     * IMPROVED: Get initialization status information.
     * This reflects whether the application has started and per-package tries are expected to be loaded.
     *
     * @return Status string
     */
    public String getInitializationStatus() {
        if (isInitializationEnsured) {
            return "INITIALIZATION_EXPECTED_COMPLETE (via RatePackageCache @PostConstruct with per-package tries)";
        } else {
            return "APPLICATION_NOT_FULLY_READY_OR_CACHE_ISSUE";
        }
    }

    /**
     * IMPROVED: Force initialization (delegates to cache refresh)
     *
     * @return CompletableFuture that completes when initialization is done
     */
    public CompletableFuture<Void> forceInitialization() {
        log.info("Force initialization requested - delegating to cache refresh");
        return refreshTriesAndCache();
    }
}
}
