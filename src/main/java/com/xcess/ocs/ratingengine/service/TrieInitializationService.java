package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.ratingengine.trie.RadixDestinationOnlyTrie;
import com.xcess.ocs.ratingengine.trie.RadixSourceDestinationTrie;
import com.xcess.ocs.repository.RatePackageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service responsible for initializing and populating the Radix Trie data structures
 * with rate data from the database at application startup.
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrieInitializationService {

    private final RatePackageRepository ratePackageRepository;
    private final RadixSourceDestinationTrie sourceDestinationTrie;
    private final RadixDestinationOnlyTrie destinationOnlyTrie;

    private volatile boolean isInitialized = false;
    private volatile boolean isInitializing = false;
    
    /**
     * Initialize tries when application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void initializeTriesOnStartup() {
        log.info("Application ready event received, initializing tries...");
        initializeTries().thenRun(() -> {
            log.info("Trie initialization completed successfully");
        }).exceptionally(throwable -> {
            log.error("Failed to initialize tries", throwable);
            return null;
        });
    }

    /**
     * Initialize tries with rate data from database
     *
     * @return CompletableFuture that completes when initialization is done
     */
    @Transactional(readOnly = true)
    public CompletableFuture<Void> initializeTries() {
        if (isInitializing) {
            log.warn("Trie initialization already in progress");
            return CompletableFuture.completedFuture(null);
        }

        if (isInitialized) {
            log.info("Tries already initialized");
            return CompletableFuture.completedFuture(null);
        }

        isInitializing = true;

        try {
            log.info("Starting trie initialization...");
            LocalDateTime startTime = LocalDateTime.now();

            // Clear existing data
            clearTries();

            // Load and populate tries
            populateTriesFromDatabase();

            isInitialized = true;
            LocalDateTime endTime = LocalDateTime.now();

            log.info("Trie initialization completed in {} ms",
                    java.time.Duration.between(startTime, endTime).toMillis());

            // Log statistics
            logTrieStatistics();

        } catch (Exception e) {
            log.error("Error during trie initialization", e);
            isInitialized = false;
            throw new RuntimeException("Failed to initialize tries", e);
        } finally {
            isInitializing = false;
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Clear all tries
     */
    private void clearTries() {
        sourceDestinationTrie.clear();
        destinationOnlyTrie.clear();
        log.debug("Cleared all tries");
    }

    /**
     * Populate tries from database
     */
    private void populateTriesFromDatabase() {
        List<RatePackage> ratePackages = ratePackageRepository.findAll();

        int sourceDestCount = 0;
        int destOnlyCount = 0;

        for (RatePackage ratePackage : ratePackages) {
            if (ratePackage.getRateDetails() != null) {
                for (RateDetails rateDetail : ratePackage.getRateDetails()) {
                    if (rateDetail.getSourcePrefix() != null && rateDetail.getDestinationPrefix() != null) {
                        // Source-destination based rate
                        sourceDestinationTrie.insert(rateDetail);
                        sourceDestCount++;
                    } else if (rateDetail.getDestinationPrefix() != null) {
                        // Destination-only based rate
                        destinationOnlyTrie.insert(rateDetail);
                        destOnlyCount++;
                    } else {
                        log.warn("Skipping rate detail with insufficient prefix data: {}", rateDetail.getRateDetailsId());
                    }
                }
            }
        }

        log.info("Populated tries: {} source-destination rates, {} destination-only rates",
                sourceDestCount, destOnlyCount);
    }

    /**
     * Log trie statistics
     */
    private void logTrieStatistics() {
        log.info("=== Trie Statistics ===");
        log.info(sourceDestinationTrie.getStatistics());
        log.info(destinationOnlyTrie.getStatistics());
    }

    /**
     * Check if tries are initialized
     *
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * Check if initialization is in progress
     *
     * @return true if initializing, false otherwise
     */
    public boolean isInitializing() {
        return isInitializing;
    }

    /**
     * Get initialization status information
     *
     * @return Status string
     */
    public String getInitializationStatus() {
        if (isInitializing) {
            return "INITIALIZING";
        } else if (isInitialized) {
            return "INITIALIZED";
        } else {
            return "NOT_INITIALIZED";
        }
    }

    /**
     * Check if the rating engine is ready for use
     *
     * @return true if engine is ready, false otherwise
     */
    public boolean isEngineReady() {
        return isInitialized;
    }

    /**
     * Refresh tries and cache (alias for forceInitialization for backward compatibility)
     *
     * @return CompletableFuture that completes when refresh is done
     */
    public CompletableFuture<Void> refreshTriesAndCache() {
        log.info("Refresh tries and cache requested");
        return forceInitialization();
    }

    /**
     * Force initialization (useful for testing or manual triggers)
     *
     * @return CompletableFuture that completes when initialization is done
     */
    public CompletableFuture<Void> forceInitialization() {
        log.info("Force initialization requested");
        isInitialized = false;
        isInitializing = false;
        return initializeTries();
    }
}
