package com.xcess.ocs.ratingengine.model;

import com.xcess.ocs.entity.RateDetails;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Represents a rate candidate found during prefix matching.
 * Contains the rate detail and metadata about the match quality.
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RateCandidate {
    
    /**
     * The rate detail that was matched
     */
    private RateDetails rateDetail;
    
    /**
     * The actual source prefix that was matched (can be different from the stored prefix)
     */
    private String matchedSourcePrefix;
    
    /**
     * The actual destination prefix that was matched
     */
    private String matchedDestinationPrefix;
    
    /**
     * Length of the source prefix that was matched
     */
    private int sourceMatchLength;
    
    /**
     * Length of the destination prefix that was matched
     */
    private int destinationMatchLength;
    
    /**
     * Indicates if this candidate came from source-destination matching (Phase 1)
     * or destination-only matching (Phase 2)
     */
    private boolean isSourceDestinationMatch;
    
    /**
     * The creation timestamp of the associated rate package for tie-breaking
     */
    private LocalDateTime ratePackageCreatedAt;
    
    /**
     * Check if this rate candidate is valid for the given call timestamp
     * 
     * @param callTimestamp The timestamp of the call
     * @return true if the rate is valid at the given time, false otherwise
     */
    public boolean isValidAt(LocalDateTime callTimestamp) {
        if (rateDetail == null || callTimestamp == null) {
            return false;
        }
        
        LocalDateTime startTime = rateDetail.getStartTime();
        LocalDateTime endTime = rateDetail.getEndTime();
        
        return (startTime == null || !callTimestamp.isBefore(startTime)) &&
               (endTime == null || !callTimestamp.isAfter(endTime));
    }
    
    /**
     * Get the total match score for comparison purposes.
     * Higher score indicates better match.
     * 
     * @return Match score
     */
    public int getMatchScore() {
        int score = destinationMatchLength * 1000; // Destination match is primary
        
        if (isSourceDestinationMatch) {
            score += sourceMatchLength * 100; // Source match is secondary
            score += 10; // Bonus for source-destination match
        }
        
        return score;
    }
    
    /**
     * Compare this candidate with another for best match selection
     * 
     * @param other The other candidate to compare with
     * @return Negative if this is better, positive if other is better, 0 if equal
     */
    public int compareTo(RateCandidate other) {
        if (other == null) {
            return -1;
        }
        
        // First, compare by match type (source-destination vs destination-only)
        if (this.isSourceDestinationMatch != other.isSourceDestinationMatch) {
            return this.isSourceDestinationMatch ? -1 : 1;
        }
        
        // If both are source-destination matches, compare source prefix length
        if (this.isSourceDestinationMatch && other.isSourceDestinationMatch) {
            int sourceComparison = Integer.compare(other.sourceMatchLength, this.sourceMatchLength);
            if (sourceComparison != 0) {
                return sourceComparison;
            }
        }
        
        // Compare destination prefix length (longer is better)
        int destComparison = Integer.compare(other.destinationMatchLength, this.destinationMatchLength);
        if (destComparison != 0) {
            return destComparison;
        }
        
        // If all else is equal, compare by rate package creation time (more recent is better)
        if (this.ratePackageCreatedAt != null && other.ratePackageCreatedAt != null) {
            return other.ratePackageCreatedAt.compareTo(this.ratePackageCreatedAt);
        } else if (this.ratePackageCreatedAt != null) {
            return -1;
        } else if (other.ratePackageCreatedAt != null) {
            return 1;
        }
        
        return 0;
    }
    
    /**
     * Create a rate candidate from a rate detail with match information
     * 
     * @param rateDetail The rate detail
     * @param matchedSourcePrefix The matched source prefix
     * @param matchedDestinationPrefix The matched destination prefix
     * @param isSourceDestinationMatch Whether this is a source-destination match
     * @param ratePackageCreatedAt The creation timestamp of the associated rate package
     * @return A new RateCandidate instance
     */
    public static RateCandidate create(RateDetails rateDetail, 
                                     String matchedSourcePrefix, 
                                     String matchedDestinationPrefix, 
                                     boolean isSourceDestinationMatch,
                                     LocalDateTime ratePackageCreatedAt) {
        return RateCandidate.builder()
                .rateDetail(rateDetail)
                .matchedSourcePrefix(matchedSourcePrefix)
                .matchedDestinationPrefix(matchedDestinationPrefix)
                .sourceMatchLength(matchedSourcePrefix != null ? matchedSourcePrefix.length() : 0)
                .destinationMatchLength(matchedDestinationPrefix != null ? matchedDestinationPrefix.length() : 0)
                .isSourceDestinationMatch(isSourceDestinationMatch)
                .ratePackageCreatedAt(ratePackageCreatedAt)
                .build();
    }
    
    @Override
    public String toString() {
        return String.format("RateCandidate{rateDetailId=%s, srcMatch='%s'(%d), destMatch='%s'(%d), isSDMatch=%s, pkgCreatedAt=%s, score=%d}", 
                rateDetail != null ? rateDetail.getRateDetailsId() : "null",
                matchedSourcePrefix, sourceMatchLength,
                matchedDestinationPrefix, destinationMatchLength,
                isSourceDestinationMatch, 
                ratePackageCreatedAt != null ? ratePackageCreatedAt.toString() : "null",
                getMatchScore());
    }
}
