package com.xcess.ocs.repository;

import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.entity.ServiceType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.xcess.ocs.entity.Type;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RatePackageRepository extends JpaRepository<RatePackage, Long> {
    @Query("SELECT p FROM RatePackage p LEFT JOIN FETCH p.rateDetails")
    List<RatePackage> findAll();
    boolean existsByPackageNameAndIsDeletedFalse(String packageName);

    boolean existsByPulse_PulseIdAndIsDeletedFalse(Long pulseId);

    Optional<List<RatePackage>> findByRatePackageType(RatePackageType type);

    @Query("SELECT rp FROM RatePackage rp WHERE " +
            "(:searchTerm IS NULL OR LOWER(rp.packageName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(rp.packageDesc) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(rp.priceRounding) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(rp.rounding) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(rp.ratePackageType) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(rp.type) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
            "(:serviceType IS NULL OR rp.serviceType = :serviceType) AND " +
            "rp.isDeleted = false")
    Page<RatePackage> searchRatePackages(
            @Param("searchTerm") String searchTerm,
            @Param("serviceType") ServiceType serviceType,
            Pageable pageable
    );
    Optional<RatePackage> findByServiceTypeAndRatePackageTypeAndSubtypeIgnoreCase(
            ServiceType serviceType,
            RatePackageType ratePackageType,
            String subtype
    );

    boolean existsByServiceTypeAndRatePackageTypeAndSubtypeIgnoreCase(
            ServiceType serviceType,
            RatePackageType ratePackageType,
            String subtype
    );
}