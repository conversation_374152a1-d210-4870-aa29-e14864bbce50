package com.xcess.ocs.repository;

//import com.partner.partner_management_backend.model.Account;

import com.xcess.ocs.entity.Account;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AccountRepository extends JpaRepository<Account, Long> {

    boolean existsByPartner_PartnerIdAndIsDeletedFalse(Long partnerId);

    boolean existsByProductPlan_ProductPlanIdAndIsDeletedFalse(Long productPlanId);

    List<Account> findByPartnerPartnerId(Long partnerId);

    boolean existsByAccountCodeAndIsDeletedFalse(String accountCode);

    @Query("SELECT a FROM Account a " +
            "LEFT JOIN a.partner p " +
            "LEFT JOIN a.productPlan pp " +
            "WHERE " +
            "(:searchTerm IS NULL OR " +
            "LOWER(a.accountCode) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(p.partnerName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(pp.name) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
            "(:partnerType IS NULL OR a.partnerType = :partnerType) AND " +
            "a.isDeleted = false")
    Page<Account> searchAccounts(
            @Param("searchTerm") String searchTerm,
            @Param("partnerType") String partnerType,
            Pageable pageable
    );
}
