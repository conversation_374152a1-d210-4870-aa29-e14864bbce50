package com.xcess.ocs.repository;

import com.xcess.ocs.entity.Country;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface CountryRepository extends JpaRepository<Country, Long> {
    List<Country> findByCountryCode(String countryCode);

    Optional<Country> findByName(String name);

    boolean existsByNameAndIsDeletedFalse(String name);

    @Query("SELECT c FROM Country c WHERE " +
           "(:searchTerm IS NULL OR LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.countryCode) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "c.isDeleted = false")
    Page<Country> searchCountries(@Param("searchTerm") String searchTerm, Pageable pageable);
}
