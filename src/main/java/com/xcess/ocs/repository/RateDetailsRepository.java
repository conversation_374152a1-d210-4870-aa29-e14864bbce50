package com.xcess.ocs.repository;

import com.xcess.ocs.entity.RateDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RateDetailsRepository extends JpaRepository<RateDetails, Long> {

    // For destination-based rate packages
    boolean existsByDestinationPrefixAndRatePackageRatePackageId(String destinationPrefix, Long ratePackageId);

    // For source-destination based rate packages
    boolean existsBySourcePrefixAndDestinationPrefixAndRatePackageRatePackageId(String sourcePrefix,
            String destinationPrefix, Long ratePackageId);

    List<RateDetails> findByRatePackageRatePackageId(Long ratePackageId);

    @Query("SELECT MAX(r.currentVersion) FROM RateDetails r WHERE r.ratePackage.ratePackageId = :ratePackageId")
    Integer findMaxCurrentVersionByRatePackageId(Long ratePackageId);

    @Modifying
    @Query("DELETE FROM RateDetails rd WHERE rd.ratePackage.ratePackageId = :ratePackageId")
    void deleteByRatePackageId(@Param("ratePackageId") Long ratePackageId);

}