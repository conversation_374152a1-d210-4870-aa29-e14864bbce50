package com.xcess.ocs.repository;

import com.xcess.ocs.entity.RatePackageAssociation;
import org.springframework.data.jpa.repository.JpaRepository;

public interface RatePackageAssociationRepository extends JpaRepository<RatePackageAssociation, Long> {
    boolean existsByRatePackageGroup_RatePackageGroupIdAndIsDeletedFalse(Long ratePackageGroupId);

    boolean existsByRatePackage_RatePackageIdAndIsDeletedFalse(Long ratePackageId);
}
