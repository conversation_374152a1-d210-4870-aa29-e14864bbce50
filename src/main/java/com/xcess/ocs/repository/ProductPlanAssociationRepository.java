package com.xcess.ocs.repository;

import com.xcess.ocs.entity.ProductPlanAssociation;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ProductPlanAssociationRepository extends JpaRepository<ProductPlanAssociation, Long> {
    boolean existsByProductPlan_ProductPlanIdAndIsDeletedFalse(Long productPlanId);

    boolean existsByRatePackageGroup_RatePackageGroupIdAndIsDeletedFalse(Long ratePackageGroupId);
}
