package com.xcess.ocs.repository;

import com.xcess.ocs.entity.Prefix;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PrefixRepository extends JpaRepository<Prefix, Long> {
    /**
     * Find all prefixes by country ID
     * 
     * @param countryId ID of the country
     * @return List of prefixes for the given country
     */
    List<Prefix> findByCountryCountryId(Long countryId);

    /**
     * Check if a prefix already exists
     * 
     * @param prefix The prefix to check
     * @return true if the prefix exists, false otherwise
     */
    boolean existsByPrefixAndIsDeletedFalse(String prefix);

    boolean existsByCountry_CountryIdAndIsDeletedFalse(Long partnerId);

    @Query("SELECT p FROM Prefix p WHERE " +
           "(:searchTerm IS NULL OR " +
           "LOWER(p.prefix) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.prefixName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.country.name) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "p.isDeleted = false")
    Page<Prefix> searchPrefixes(@Param("searchTerm") String searchTerm, Pageable pageable);
}
