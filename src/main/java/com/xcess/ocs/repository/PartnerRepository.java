package com.xcess.ocs.repository;

import com.xcess.ocs.entity.Partner;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerRepository extends JpaRepository<Partner, Long> {
    boolean existsByPartnerNameAndIsDeletedFalse(@NotBlank(message = "Partner name is required") String partnerName);

    @Query("SELECT p FROM Partner p WHERE " +
            "(:partnerName IS NULL OR LOWER(p.partnerName) LIKE LOWER(CONCAT('%', :partnerName, '%'))) AND " +
            "(:partnerType IS NULL OR p.partnerType = :partnerType) AND " +
            "p.isDeleted = false")
    Page<Partner> searchPartners(
            @Param("partnerName") String partnerName,
            @Param("partnerType") String partnerType,
            Pageable pageable
    );
}