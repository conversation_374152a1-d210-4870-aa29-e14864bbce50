package com.xcess.ocs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(
        name = "3. Partner",
        description = "Schema to hold details of a partner"
)
public class PartnerDTO {
    @Schema(
            description = "ID of the partner",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private Long partnerId;

    @Schema(
            description = "Name of the partner",
            example = "John Doe"
    )
    @NotBlank(message = "Partner name is required")
    private String partnerName;

    @Schema(
            description = "Type of the partner",
            example = "CUSTOMER",
            allowableValues = {"CUSTOMER", "VENDOR", "BOTH"}
    )
    @NotBlank(message = "Partner type is required")
    private String partnerType;
}