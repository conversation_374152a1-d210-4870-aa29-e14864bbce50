package com.xcess.ocs.dto.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema(
        name = "18. Partner SearchDTO",
        description = "Schema to hold Partner search details"
)
public class PartnerSearchDTO {
        @Schema(
                description = "Partner name to search",
                example = "John",
                nullable = true
        )
        private String partnerName;

        @Schema(
                description = "Partner type to filter",
                example = "CUSTOMER",
                allowableValues = {"CUSTOMER", "VENDOR", "BOTH"},
                nullable = true
        )
        private String partnerType;
}