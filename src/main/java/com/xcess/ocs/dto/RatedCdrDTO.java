package com.xcess.ocs.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a raw CDR message coming from Kafka before mapping fields.
 * Fields correspond to those in Source_CDR_configuration table.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RatedCdrDTO {
    private String startTime;
    private String endTime;
    private String callingNumber;
    private String calledNumber;
    private String incomingAccountId;
    private String outgoingAccountId;
}

