package com.xcess.ocs.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "13. Product Plan Association", description = "Association between a product plan and a rate package group")
public class ProductPlanAssociationDTO {
    @Schema(description = "Rate Package Group ID", example = "1")
    @NotNull(message = "Rate Package Group ID is required")
    private Long ratePackageGroupId;

    @Schema(description = "Name of the Rate Package Group", example = "Premium Voice & SMS",accessMode = Schema.AccessMode.READ_ONLY)
    private String ratePackageGroupName;

    @Schema(description = "Start time for the association",type="string" ,example = "2023-01-01 06:30:30")
    @NotNull(message = "Start time is required")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "End time for the association",type="string" , example = "2023-12-31 23:59:59")
    @NotNull(message = "End time is required")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}