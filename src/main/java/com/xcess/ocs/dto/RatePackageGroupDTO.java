package com.xcess.ocs.dto;

import com.xcess.ocs.entity.RatePackage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "11. Rate Package Group", description = "Schema to hold Rate package group details")
public class RatePackageGroupDTO {

    @Schema(description = "Rate package group ID", accessMode = Schema.AccessMode.READ_ONLY)
    private Long ratePackageGroupId;

    @Schema(description = "Rate package group name", example = "Premium Voice & SMS")
    @NotEmpty(message = "Rate package group name is required")
    private String ratePackageGroupName;

    @Schema(description = "Description of the rate package group", example = "Combined voice and SMS package for premium customers")
    @NotEmpty(message = "Description is required")
    private String description;

    @Schema(description = "Type of the package", example = "SELLING")
    @NotNull(message = "Package type is required")
    private String packageType;

    @Schema(description = "Rate packages associated with the rate package group")
    @NotEmpty(message = "At least one rate package is required")
    private List<RatePackageAssociationDTO> ratePackages;
}