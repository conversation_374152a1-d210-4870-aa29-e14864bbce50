package com.xcess.ocs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

@Data
@Schema(name = "14. Account", description = "Account details of the customer or vendor")
public class AccountDTO {
    @Schema(description = "Account ID", accessMode = Schema.AccessMode.READ_ONLY)
    private Long accountId;

    @Schema(description = "Account code", example = "ACC00")
    @NotBlank(message = "Account code is required")
    @Size(min = 4, max = 30, message = "Account code must be between 4 and 30 characters")
    @Pattern(
            regexp = "^[A-Za-z][A-Za-z0-9_-]{3,29}$",
            message = "Account code must start with a letter and contain only letters, digits, underscores (_) or hyphens (-)"
    )
    private String accountCode;


    @Schema(description = "ID of the partner", example = "1")
    @NotNull(message = "Partner ID is required")
    private Long partnerId;

    @Schema(description = "Name of the partner", example = "Acme Corp", accessMode = Schema.AccessMode.READ_ONLY)
    private String partnerName;

    @Schema(description = "Type of the partner", example = "VENDOR")
    @NotBlank(message = "Partner type is required")
    @Pattern(regexp = "CUSTOMER|VENDOR", message = "Partner type must be either CUSTOMER or VENDOR")
    private String partnerType;

    @Schema(description = "ID of the product plan", example = "1")
    @NotNull(message = "Product plan ID is required")
    private Long productPlanId;

    @Schema(description = "Name of the product plan", example = "Premium Plan", accessMode = Schema.AccessMode.READ_ONLY)
    private String productPlanName;
}