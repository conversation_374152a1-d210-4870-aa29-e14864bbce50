package com.xcess.ocs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "5. Prefix" , description = "Schema to hold details of Prefix")
public class PrefixDTO {
    @Schema(description = "ID of the prefix", accessMode = Schema.AccessMode.READ_ONLY)
    private Long prefixId; // Used in response

    @Schema(
            description = "Name of the country",
            example = "Argentina"
    )
    @NotBlank(message = "Country name cannot be empty")
    private String countryName; // Required for creation and response

    @Schema(
            description = "Prefix number",
            example = "5478"
    )
    @NotBlank(message = "Prefix cannot be empty")
    @Pattern(regexp = "^\\d{1,6}(-\\d{1,6})?$", message = "Invalid prefix format. It must be a number and may contain a single hyphen (e.g., '91', '1-345').")
    private String prefix;

    @Schema(
            description = "Prefix name",
            example = "aitel-India"
    )
    @NotBlank(message = "Prefix name cannot be empty")
    @Size(min = 2, max = 50, message = "Prefix name must be between 2 and 50 characters")
    @Pattern(regexp = "^[A-Za-z0-9 -]{2,50}$", message = "Invalid prefix name. Only letters, numbers, spaces, and hyphens are allowed.")
    private String prefixName;
}