package com.xcess.ocs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "4. Country", description = "Country details to store different rates for different countries")
public class CountryDTO {
    @Schema(
            description = "ID of the country",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private Long countryId;

    @Schema(
            description = "Name of the country",
            example = "India"
    )
    @NotBlank(message = "Country name cannot be empty")
    @Pattern(regexp = "^[A-Z][a-zA-Z ]*$", message = "Country name must start with a capital letter and contain only letters and spaces")
    private String name;

    @Schema(
            description = "Country code",
            example = "91"
    )
    @NotBlank(message = "Country code cannot be empty")
    private String countryCode;
}
