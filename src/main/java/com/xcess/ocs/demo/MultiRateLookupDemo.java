package com.xcess.ocs.demo;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Demo showing how to find all valid rates for different matching prefixes
 * of a phone number at a given date.
 */
public class MultiRateLookupDemo {

    /**
     * Represents a rate entry with date range information
     */
    static class RateEntry {
        private String prefix;
        private double rate;
        private LocalDateTime startDate;
        private LocalDateTime endDate;
        private String description; // Added to help identify the rate
        
        public RateEntry(String prefix, double rate, LocalDateTime startDate, 
                        LocalDateTime endDate, String description) {
            this.prefix = prefix;
            this.rate = rate;
            this.startDate = startDate;
            this.endDate = endDate;
            this.description = description;
        }
        
        public String getPrefix() {
            return prefix;
        }
        
        public double getRate() {
            return rate;
        }
        
        public LocalDateTime getStartDate() {
            return startDate;
        }
        
        public LocalDateTime getEndDate() {
            return endDate;
        }
        
        public String getDescription() {
            return description;
        }
        
        @Override
        public String toString() {
            return "RateEntry{" +
                    "prefix='" + prefix + '\'' +
                    ", rate=" + rate +
                    ", description='" + description + '\'' +
                    ", startDate=" + startDate +
                    ", endDate=" + endDate +
                    '}';
        }
    }

    /**
     * Node in the Radix Tree structure
     */
    static class RadixTreeNode {
        private String prefix;
        private Map<Character, RadixTreeNode> children;
        private List<RateEntry> rateEntries;
        private String fullPath; // Track the full path to this node
        
        public RadixTreeNode() {
            this("", "");
        }
        
        public RadixTreeNode(String prefix) {
            this(prefix, prefix);
        }
        
        public RadixTreeNode(String prefix, String fullPath) {
            this.prefix = prefix;
            this.fullPath = fullPath;
            this.children = new HashMap<>();
            this.rateEntries = new ArrayList<>();
        }
        
        public String getPrefix() {
            return prefix;
        }
        
        public String getFullPath() {
            return fullPath;
        }
        
        public void setFullPath(String fullPath) {
            this.fullPath = fullPath;
        }
        
        public Map<Character, RadixTreeNode> getChildren() {
            return children;
        }
        
        public List<RateEntry> getRateEntries() {
            return rateEntries;
        }
        
        public void addRateEntry(RateEntry entry) {
            rateEntries.add(entry);
            rateEntries.sort((a, b) -> b.getStartDate().compareTo(a.getStartDate()));
        }
    }

    /**
     * Main RadixTree implementation that supports finding all valid rates
     */
    static class MultiRateLookup {
        private final RadixTreeNode root;
        
        public MultiRateLookup() {
            root = new RadixTreeNode();
        }
        
        /**
         * Insert a new rate entry into the radix tree
         */
        public void insert(RateEntry entry) {
            String prefix = entry.getPrefix();
            if (prefix == null || prefix.isEmpty()) {
                throw new IllegalArgumentException("Prefix cannot be null or empty");
            }
            
            insertRecursive(root, prefix, 0, entry);
        }
        
        private void insertRecursive(RadixTreeNode node, String prefix, int index, RateEntry entry) {
            // If we've processed all characters in the prefix
            if (index == prefix.length()) {
                node.addRateEntry(entry);
                return;
            }
            
            char currentChar = prefix.charAt(index);
            RadixTreeNode child = node.getChildren().get(currentChar);
            
            // If no child exists for this character, create a new one
            if (child == null) {
                String childPrefix = prefix.substring(index);
                String fullPath = prefix;
                child = new RadixTreeNode(childPrefix, fullPath);
                node.getChildren().put(currentChar, child);
                child.addRateEntry(entry);
                return;
            }
            
            // Find the common prefix between the existing child's prefix and the remaining part of our prefix
            String childPrefix = child.getPrefix();
            String remainingPrefix = prefix.substring(index);
            int commonPrefixLength = findCommonPrefixLength(childPrefix, remainingPrefix);
            
            // If they share the complete child prefix
            if (commonPrefixLength == childPrefix.length()) {
                // Continue insertion in the child node
                insertRecursive(child, prefix, index + commonPrefixLength, entry);
            } else {
                // Split the child node
                String commonPrefix = childPrefix.substring(0, commonPrefixLength);
                String childRemainder = childPrefix.substring(commonPrefixLength);
                String entryRemainder = remainingPrefix.substring(commonPrefixLength);
                
                // Calculate full paths
                String newNodeFullPath = prefix.substring(0, index + commonPrefixLength);
                
                // Create a new intermediate node with the common prefix
                RadixTreeNode newNode = new RadixTreeNode(commonPrefix, newNodeFullPath);
                node.getChildren().put(currentChar, newNode);
                
                // Move the existing child under the new node
                String existingChildFullPath = child.getFullPath();
                RadixTreeNode existingChild = new RadixTreeNode(childRemainder, existingChildFullPath);
                existingChild.getRateEntries().addAll(child.getRateEntries());
                existingChild.getChildren().putAll(child.getChildren());
                
                if (!childRemainder.isEmpty()) {
                    newNode.getChildren().put(childRemainder.charAt(0), existingChild);
                } else {
                    // The new intermediate node inherits the rate entries from the child
                    newNode.getRateEntries().addAll(child.getRateEntries());
                }
                
                // Add the new entry to a new node under the intermediate node if needed
                if (!entryRemainder.isEmpty()) {
                    RadixTreeNode newEntryNode = new RadixTreeNode(entryRemainder, prefix);
                    newEntryNode.addRateEntry(entry);
                    newNode.getChildren().put(entryRemainder.charAt(0), newEntryNode);
                } else {
                    // The new entry belongs to the intermediate node
                    newNode.addRateEntry(entry);
                }
            }
        }
        
        private int findCommonPrefixLength(String str1, String str2) {
            int minLength = Math.min(str1.length(), str2.length());
            for (int i = 0; i < minLength; i++) {
                if (str1.charAt(i) != str2.charAt(i)) {
                    return i;
                }
            }
            return minLength;
        }
        
        /**
         * Find all valid rates for a phone number at a specific date
         * Returns a map of prefixes to their corresponding valid rate entries
         */
        public Map<String, RateEntry> findAllValidRates(String phoneNumber, LocalDateTime date) {
            if (phoneNumber == null || phoneNumber.isEmpty()) {
                throw new IllegalArgumentException("Phone number cannot be null or empty");
            }
            
            Map<String, RateEntry> validRates = new LinkedHashMap<>();
            List<RadixTreeNode> matchedNodes = new ArrayList<>();
            
            // Find all nodes that match a prefix of the phone number
            findAllMatchingPrefixes(root, phoneNumber, 0, "", matchedNodes);
            
            // Sort matched nodes by prefix length (descending) for a clear hierarchy
            matchedNodes.sort((a, b) -> Integer.compare(b.getFullPath().length(), a.getFullPath().length()));
            
            // For each matching node, find valid rate entries for the given date
            for (RadixTreeNode node : matchedNodes) {
                String fullPrefix = node.getFullPath();
                RateEntry validEntry = findMatchingRateEntry(node.getRateEntries(), date);
                
                if (validEntry != null) {
                    validRates.put(fullPrefix, validEntry);
                }
            }
            
            return validRates;
        }
        
        /**
         * Find all nodes where the prefix matches the beginning of the phone number
         */
        private void findAllMatchingPrefixes(RadixTreeNode node, String phoneNumber, int index, 
                                            String currentPrefix, List<RadixTreeNode> matchedNodes) {
            // If this node contains rate entries, it's a possible match
            if (!node.getRateEntries().isEmpty()) {
                matchedNodes.add(node);
            }
            
            // If we've processed the entire phone number, stop recursion
            if (index >= phoneNumber.length()) {
                return;
            }
            
            // Try to find a matching child
            char currentChar = phoneNumber.charAt(index);
            RadixTreeNode child = node.getChildren().get(currentChar);
            
            if (child != null) {
                String childPrefix = child.getPrefix();
                // Check if the child's prefix is a prefix of the remaining phone number
                String remainingNumber = phoneNumber.substring(index);
                
                if (remainingNumber.startsWith(childPrefix)) {
                    // Continue search in the child node
                    findAllMatchingPrefixes(child, phoneNumber, index + childPrefix.length(), 
                            currentPrefix + childPrefix, matchedNodes);
                }
            }
        }
        
        /**
         * Find a rate entry that matches the given date
         */
        private RateEntry findMatchingRateEntry(List<RateEntry> entries, LocalDateTime date) {
            for (RateEntry entry : entries) {
                if ((entry.getStartDate().isBefore(date) || entry.getStartDate().isEqual(date)) && 
                    (entry.getEndDate().isAfter(date) || entry.getEndDate().isEqual(date))) {
                    return entry;
                }
            }
            return null;
        }
    }

    /**
     * Main method demonstrating how to find all valid rates for different prefixes
     */
    public static void main(String[] args) {
        System.out.println("=== Multi-Rate Lookup Demo ===");
        System.out.println("This demo shows how to find all valid rates for different matching prefixes\n");
        
        // Create the rate lookup tree
        MultiRateLookup rateLookup = new MultiRateLookup();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // Create a testing date
        LocalDateTime testDate = LocalDateTime.parse("2023-06-15 12:00:00", formatter);
        
        System.out.println("Step 1: Inserting rate entries for overlapping prefixes");
        System.out.println("---------------------------------------------------");
        
        // Insert rate entries for India with increasingly specific prefixes
        System.out.println("• Adding rate for India (91): $0.04");
        rateLookup.insert(new RateEntry("91", 0.04, 
                LocalDateTime.parse("2023-01-01 00:00:00", formatter),
                LocalDateTime.parse("2023-12-31 23:59:59", formatter),
                "India - Base Rate"));
        
        System.out.println("• Adding rate for Mumbai (9122): $0.035");
        rateLookup.insert(new RateEntry("9122", 0.035, 
                LocalDateTime.parse("2023-01-01 00:00:00", formatter),
                LocalDateTime.parse("2023-12-31 23:59:59", formatter),
                "Mumbai (Maharashtra)"));
        
        System.out.println("• Adding rate for Delhi (9111): $0.032");
        rateLookup.insert(new RateEntry("9111", 0.032, 
                LocalDateTime.parse("2023-01-01 00:00:00", formatter),
                LocalDateTime.parse("2023-12-31 23:59:59", formatter),
                "Delhi - Standard"));
                
        System.out.println("• Adding special promotion rate for Delhi (9111): $0.028 (valid Apr-Sep 2023)");
        rateLookup.insert(new RateEntry("9111", 0.028, 
                LocalDateTime.parse("2023-04-01 00:00:00", formatter),
                LocalDateTime.parse("2023-09-30 23:59:59", formatter),
                "Delhi - Business Promotion"));
                
        System.out.println("• Adding rate for Delhi Business District (91114): $0.025");
        rateLookup.insert(new RateEntry("91114", 0.025, 
                LocalDateTime.parse("2023-01-01 00:00:00", formatter),
                LocalDateTime.parse("2023-12-31 23:59:59", formatter),
                "Delhi Business District"));
                
        System.out.println("• Adding expired rate for Karnataka (9198): $0.045 (expired)");
        rateLookup.insert(new RateEntry("9198", 0.045, 
                LocalDateTime.parse("2022-01-01 00:00:00", formatter),
                LocalDateTime.parse("2022-12-31 23:59:59", formatter),
                "Karnataka - Expired Rate"));
                
        System.out.println("• Adding current rate for Karnataka (9198): $0.038");
        rateLookup.insert(new RateEntry("9198", 0.038, 
                LocalDateTime.parse("2023-01-01 00:00:00", formatter),
                LocalDateTime.parse("2023-12-31 23:59:59", formatter),
                "Karnataka - Current Rate"));
        
        System.out.println("\nStep 2: Performing multi-rate lookups for different numbers");
        System.out.println("------------------------------------------------------");
        
        // Test with different phone numbers
        performMultiRateLookup(rateLookup, "911142345678", testDate, formatter);
        System.out.println("\n------------------------------------------------------");
        
        performMultiRateLookup(rateLookup, "911156789012", testDate, formatter);
        System.out.println("\n------------------------------------------------------");
        
        performMultiRateLookup(rateLookup, "919845678901", testDate, formatter);
        System.out.println("\n------------------------------------------------------");
        
        performMultiRateLookup(rateLookup, "912234567890", testDate, formatter);
    }
    
    /**
     * Helper method to perform and print a multi-rate lookup operation
     */
    private static void performMultiRateLookup(MultiRateLookup rateLookup, 
                                             String phoneNumber, 
                                             LocalDateTime date,
                                             DateTimeFormatter formatter) {
        System.out.println("\nLooking up all valid rates for: " + phoneNumber + " on " + date.format(formatter));
        
        Map<String, RateEntry> validRates = rateLookup.findAllValidRates(phoneNumber, date);
        
        if (validRates.isEmpty()) {
            System.out.println("  No valid rates found");
        } else {
            System.out.println("  Found " + validRates.size() + " valid rates:");
            System.out.println("  -------------------------------");
            
            // Display rates in order of prefix specificity (longest to shortest)
            int count = 1;
            for (Map.Entry<String, RateEntry> entry : validRates.entrySet()) {
                String prefix = entry.getKey();
                RateEntry rate = entry.getValue();
                
                System.out.println("  " + count + ". Prefix: " + prefix);
                System.out.println("     Rate: $" + rate.getRate());
                System.out.println("     Description: " + rate.getDescription());
                System.out.println("     Valid period: " + rate.getStartDate().format(formatter) + 
                                  " to " + rate.getEndDate().format(formatter));
                System.out.println();
                count++;
            }
            
            // Identify the best rate (typically the longest prefix match)
            if (!validRates.isEmpty()) {
                Map.Entry<String, RateEntry> bestMatch = validRates.entrySet().iterator().next();
                System.out.println("  BEST MATCH:");
                System.out.println("  -----------");
                System.out.println("  Prefix: " + bestMatch.getKey());
                System.out.println("  Rate: $" + bestMatch.getValue().getRate());
                System.out.println("  Description: " + bestMatch.getValue().getDescription());
            }
        }
    }
} 