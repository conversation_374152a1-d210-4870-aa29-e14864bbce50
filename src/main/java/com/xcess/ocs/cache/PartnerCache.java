package com.xcess.ocs.cache;

import com.xcess.ocs.dto.PartnerDTO;
import com.xcess.ocs.entity.Partner;
import com.xcess.ocs.mapper.PartnerMapper;
import com.xcess.ocs.repository.PartnerRepository;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class PartnerCache {

    private final PartnerRepository partnerRepository;
    private final PartnerMapper partnerMapper;

    //Store partners in cache with `partnerName` as the key
    private final Map<String, PartnerDTO> partnerCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void preloadCache() {
        log.info("Preloading partner cache...");
        List<Partner> partners = partnerRepository.findAll();
        for (Partner partner : partners) {
            PartnerDTO dto = partnerMapper.toDto(partner);
            partnerCache.put(partner.getPartnerName(), dto);
        }
        log.debug("Preloaded {} partners into cache", partnerCache.size());
        log.info("{} Partners", partnerCache.size());
    }

    public List<PartnerDTO> getAllPartners() {
        return new ArrayList<>(partnerCache.values());
    }

    public PartnerDTO getPartnerByName(String name) {
        return partnerCache.get(name);
    }

    public void addToCache(PartnerDTO dto) {
        partnerCache.put(dto.getPartnerName(), dto);
    }

    public void updateCache(PartnerDTO dto) {
        partnerCache.put(dto.getPartnerName(), dto);
    }

    public void removeFromCache(String name) {
        partnerCache.remove(name);
    }

    // Auto-refresh cache every 10 minutes
    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing partner cache...");
        partnerCache.clear();
        preloadCache();
    }
}