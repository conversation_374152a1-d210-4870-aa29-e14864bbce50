package com.xcess.ocs.cache;

import com.xcess.ocs.dto.RatePackageDTO;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.mapper.RatePackageMapper;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import com.xcess.ocs.ratingengine.trie.RatePackageDestinationOnlyTrie;
import com.xcess.ocs.ratingengine.trie.RatePackageSourceDestinationTrie;
import com.xcess.ocs.repository.RatePackageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SIMPLIFIED: Rate Package Cache with direct trie management.
 * Eliminates unnecessary wrapper classes for better performance and simplicity.
 *
 * Key Improvements:
 * - Direct trie management without wrapper classes
 * - Each rate package has its own isolated tries
 * - Thread-safe operations with concurrent collections
 * - Reduced object creation and method call overhead
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RatePackageCache {
    private final RatePackageRepository ratePackageRepository;

    // Cache to store all RatePackageDTOs by ID
    private final Map<Long, RatePackageDTO> packageDtoCache = new ConcurrentHashMap<>();

    // SIMPLIFIED: Direct trie caches without wrapper
    private final Map<Long, RatePackageSourceDestinationTrie> sourceDestTrieCache = new ConcurrentHashMap<>();
    private final Map<Long, RatePackageDestinationOnlyTrie> destOnlyTrieCache = new ConcurrentHashMap<>();

    /**
     * SIMPLIFIED: Direct trie initialization without wrapper classes
     */
    @PostConstruct
    public void preloadCache() {
        log.info("Preloading rate package cache and direct tries from database...");
        List<RatePackage> packages = ratePackageRepository.findAll();

        for (RatePackage ratePackage : packages) {
            Long packageId = ratePackage.getRatePackageId();

            // Cache DTO
            RatePackageDTO dto = RatePackageMapper.toDTO(ratePackage);
            packageDtoCache.put(packageId, dto);

            // Create package-specific tries directly
            RatePackageSourceDestinationTrie sourceDestTrie = new RatePackageSourceDestinationTrie(packageId);
            RatePackageDestinationOnlyTrie destOnlyTrie = new RatePackageDestinationOnlyTrie(packageId);

            // Insert rate details directly into appropriate tries
            int sourceDestCount = 0;
            int destOnlyCount = 0;

            if (ratePackage.getRateDetails() != null) {
                for (RateDetails rateDetail : ratePackage.getRateDetails()) {
                    if (hasSourceAndDestination(rateDetail)) {
                        sourceDestTrie.insert(rateDetail);
                        sourceDestCount++;
                    } else if (hasDestinationOnly(rateDetail)) {
                        destOnlyTrie.insert(rateDetail);
                        destOnlyCount++;
                    } else {
                        log.warn("Rate detail {} has insufficient prefix information for package {}",
                                rateDetail.getRateDetailsId(), packageId);
                    }
                }
            }

            // Cache tries directly
            sourceDestTrieCache.put(packageId, sourceDestTrie);
            destOnlyTrieCache.put(packageId, destOnlyTrie);

            log.debug("Loaded rate package {}: {} source-dest + {} dest-only = {} total rate details",
                    packageId, sourceDestCount, destOnlyCount, sourceDestCount + destOnlyCount);
        }

        log.info("Preloaded {} rate packages with {} source-dest tries and {} dest-only tries.",
                packageDtoCache.size(), sourceDestTrieCache.size(), destOnlyTrieCache.size());
    }

    /**
     * Get all rate package DTOs
     */
    public List<RatePackageDTO> getAllRatePackages() {
        log.debug("Fetching all rate packages from cache");
        return new ArrayList<>(packageDtoCache.values());
    }

    /**
     * Get rate package DTO by ID
     */
    public RatePackageDTO getRatePackageById(Long id) {
        log.debug("Fetching rate package DTO with ID: {} from cache", id);
        return packageDtoCache.get(id);
    }

    /**
     * SIMPLIFIED: Search across all packages for rate candidates
     * This replaces the need for RatePackageTries wrapper
     */
    public List<RateCandidate> searchAllPackages(String sourceNumber, String destinationNumber) {
        List<RateCandidate> allCandidates = new ArrayList<>();

        // Search all source-destination tries
        for (RatePackageSourceDestinationTrie sourceTrie : sourceDestTrieCache.values()) {
            List<RateCandidate> candidates = sourceTrie.search(sourceNumber, destinationNumber);
            allCandidates.addAll(candidates);
        }

        // Search all destination-only tries
        for (RatePackageDestinationOnlyTrie destTrie : destOnlyTrieCache.values()) {
            List<RateCandidate> candidates = destTrie.search(sourceNumber, destinationNumber);
            allCandidates.addAll(candidates);
        }

        log.debug("Found {} total candidates across {} packages",
                allCandidates.size(), packageDtoCache.size());
        return allCandidates;
    }

    /**
     * Get tries for a specific package (for debugging/statistics)
     */
    public RatePackageSourceDestinationTrie getSourceDestTrieById(Long packageId) {
        return sourceDestTrieCache.get(packageId);
    }

    public RatePackageDestinationOnlyTrie getDestOnlyTrieById(Long packageId) {
        return destOnlyTrieCache.get(packageId);
    }

    /**
     * SIMPLIFIED: Add to cache with direct trie management
     */
    public void addToCache(RatePackageDTO dto, RatePackage originalEntity) {
        Long packageId = dto.getRatePackageId();
        log.debug("Adding rate package ID: {} to cache with direct tries", packageId);

        // Cache DTO
        packageDtoCache.put(packageId, dto);

        // Create and populate tries directly
        RatePackageSourceDestinationTrie sourceDestTrie = new RatePackageSourceDestinationTrie(packageId);
        RatePackageDestinationOnlyTrie destOnlyTrie = new RatePackageDestinationOnlyTrie(packageId);

        if (originalEntity != null && originalEntity.getRateDetails() != null) {
            for (RateDetails rateDetail : originalEntity.getRateDetails()) {
                if (hasSourceAndDestination(rateDetail)) {
                    sourceDestTrie.insert(rateDetail);
                } else if (hasDestinationOnly(rateDetail)) {
                    destOnlyTrie.insert(rateDetail);
                }
            }
        }

        sourceDestTrieCache.put(packageId, sourceDestTrie);
        destOnlyTrieCache.put(packageId, destOnlyTrie);

        log.debug("Added rate package {} with direct tries to cache", packageId);
    }

    /**
     * SIMPLIFIED: Update cache with direct trie management
     */
    public void updateCache(RatePackageDTO dto, RatePackage originalEntity) {
        log.debug("Updating cache for rate package ID: {}", dto.getRatePackageId());
        addToCache(dto, originalEntity);
    }

    /**
     * SIMPLIFIED: Remove from cache including direct tries
     */
    public void removeFromCache(Long id) {
        log.debug("Removing rate package ID: {} from cache", id);
        packageDtoCache.remove(id);

        RatePackageSourceDestinationTrie sourceDestTrie = sourceDestTrieCache.remove(id);
        RatePackageDestinationOnlyTrie destOnlyTrie = destOnlyTrieCache.remove(id);

        if (sourceDestTrie != null) sourceDestTrie.clear();
        if (destOnlyTrie != null) destOnlyTrie.clear();

        log.debug("Removed and cleared direct tries for rate package ID: {}", id);
    }

    /**
     * SIMPLIFIED: Refresh cache with direct tries
     */
    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing rate package cache and direct tries...");

        // Clear existing caches
        packageDtoCache.clear();
        sourceDestTrieCache.values().forEach(RatePackageSourceDestinationTrie::clear);
        destOnlyTrieCache.values().forEach(RatePackageDestinationOnlyTrie::clear);
        sourceDestTrieCache.clear();
        destOnlyTrieCache.clear();

        // Reload
        preloadCache();
    }

    /**
     * Helper methods for rate detail validation
     */
    private boolean hasSourceAndDestination(RateDetails rateDetail) {
        return rateDetail.getSourcePrefix() != null &&
               !rateDetail.getSourcePrefix().trim().isEmpty() &&
               rateDetail.getDestinationPrefix() != null &&
               !rateDetail.getDestinationPrefix().trim().isEmpty();
    }

    private boolean hasDestinationOnly(RateDetails rateDetail) {
        return rateDetail.getSourcePrefix() == null &&
               rateDetail.getDestinationPrefix() != null &&
               !rateDetail.getDestinationPrefix().trim().isEmpty();
    }
}