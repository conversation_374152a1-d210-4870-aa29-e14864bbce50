package com.xcess.ocs.cache;

import com.xcess.ocs.dto.RatePackageDTO;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.mapper.RatePackageMapper;
import com.xcess.ocs.ratingengine.cache.RatePackageTries;
import com.xcess.ocs.repository.RatePackageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Enhanced Rate Package Cache with per-package trie support.
 * Follows OOP principles with proper separation of concerns.
 *
 * Key Improvements:
 * - Each rate package has its own isolated tries
 * - Proper encapsulation of cache operations
 * - Thread-safe operations with concurrent collections
 * - Clear separation between DTO cache and trie cache
 *
 * <AUTHOR> Developer
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RatePackageCache {
    private final RatePackageRepository ratePackageRepository;

    // Cache to store all RatePackageDTOs by ID
    private final Map<Long, RatePackageDTO> packageDtoCache = new ConcurrentHashMap<>();

    // Cache to store RatePackageTries by RatePackage ID
    private final Map<Long, RatePackageTries> packageTriesCache = new ConcurrentHashMap<>();

    /**
     * IMPROVED: Preload cache with per-package trie initialization
     * Each rate package gets its own isolated tries
     */
    @PostConstruct
    public void preloadCache() {
        log.info("Preloading rate package cache and per-package tries from database...");
        List<RatePackage> packages = ratePackageRepository.findAll();

        for (RatePackage ratePackage : packages) {
            Long packageId = ratePackage.getRatePackageId();

            // Cache DTO
            RatePackageDTO dto = RatePackageMapper.toDTO(ratePackage);
            packageDtoCache.put(packageId, dto);

            // Create and populate package-specific tries
            RatePackageTries packageTries = new RatePackageTries(packageId);

            if (ratePackage.getRateDetails() != null) {
                for (var rateDetail : ratePackage.getRateDetails()) {
                    packageTries.insertRateDetail(rateDetail);
                }
            }

            packageTriesCache.put(packageId, packageTries);

            log.debug("Loaded rate package {}: {} rate details into tries",
                    packageId, packageTries.getTotalRateDetailsCount());
        }

        log.info("Preloaded {} rate packages with {} total tries into cache.",
                packageDtoCache.size(), packageTriesCache.size());

        // Log statistics for each package
        packageTriesCache.values().forEach(tries ->
                log.debug("Package {} statistics: {}", tries.getRatePackageId(), tries.getStatistics()));
    }

    /**
     * Get all rate package DTOs
     */
    public List<RatePackageDTO> getAllRatePackages() {
        log.debug("Fetching all rate packages from cache");
        return new ArrayList<>(packageDtoCache.values());
    }

    /**
     * Get rate package DTO by ID
     */
    public RatePackageDTO getRatePackageById(Long id) {
        log.debug("Fetching rate package DTO with ID: {} from cache", id);
        return packageDtoCache.get(id);
    }

    /**
     * IMPROVED: Get rate package tries by ID
     */
    public RatePackageTries getRatePackageTriesById(Long id) {
        log.debug("Fetching rate package tries for ID: {} from cache", id);
        return packageTriesCache.get(id);
    }

    /**
     * IMPROVED: Get all rate package tries
     */
    public Collection<RatePackageTries> getAllRatePackageTries() {
        log.debug("Fetching all rate package tries from cache");
        return new ArrayList<>(packageTriesCache.values());
    }

    /**
     * IMPROVED: Add to cache with trie support
     */
    public void addToCache(RatePackageDTO dto, RatePackage originalEntity) {
        Long packageId = dto.getRatePackageId();
        log.debug("Adding rate package ID: {} to cache with tries", packageId);

        // Cache DTO
        packageDtoCache.put(packageId, dto);

        // Create and populate tries
        RatePackageTries packageTries = new RatePackageTries(packageId);
        if (originalEntity != null && originalEntity.getRateDetails() != null) {
            for (var rateDetail : originalEntity.getRateDetails()) {
                packageTries.insertRateDetail(rateDetail);
            }
        }
        packageTriesCache.put(packageId, packageTries);

        log.debug("Added rate package {} with {} rate details to cache",
                packageId, packageTries.getTotalRateDetailsCount());
    }

    /**
     * IMPROVED: Update cache with trie support
     */
    public void updateCache(RatePackageDTO dto, RatePackage originalEntity) {
        log.debug("Updating cache for rate package ID: {}", dto.getRatePackageId());
        addToCache(dto, originalEntity); // Reuse add logic
    }

    /**
     * IMPROVED: Remove from cache including tries
     */
    public void removeFromCache(Long id) {
        log.debug("Removing rate package ID: {} from cache", id);
        packageDtoCache.remove(id);
        RatePackageTries removedTries = packageTriesCache.remove(id);
        if (removedTries != null) {
            removedTries.clear(); // Clean up tries
            log.debug("Removed and cleared tries for rate package ID: {}", id);
        }
    }

    /**
     * IMPROVED: Refresh cache with tries
     */
    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing rate package cache and tries...");

        // Clear existing caches
        packageDtoCache.clear();
        packageTriesCache.values().forEach(RatePackageTries::clear);
        packageTriesCache.clear();

        // Reload
        preloadCache();
    }
}