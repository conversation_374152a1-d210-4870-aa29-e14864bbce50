package com.xcess.ocs.cache;

import com.xcess.ocs.dto.RatePackageDTO;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.mapper.RatePackageMapper;
import com.xcess.ocs.repository.RatePackageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class RatePackageCache {
    private final RatePackageRepository ratePackageRepository;

    // Cache to store all RatePackageDTOs by ID
    private final Map<Long, RatePackageDTO> packageDtoCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void preloadCache() {
        log.info("Preloading rate package cache from database...");
        List<RatePackage> packages = ratePackageRepository.findAll();
        for (RatePackage ratePackage : packages) {
            RatePackageDTO dto = RatePackageMapper.toDTO(ratePackage);
            packageDtoCache.put(ratePackage.getRatePackageId(), dto);
        }
        log.info("Preloaded {} rate packages into cache.", packageDtoCache.size());
    }

    public List<RatePackageDTO> getAllRatePackages() {
        log.debug("Fetching all rate packages from cache");
        return new ArrayList<>(packageDtoCache.values());
    }

    public RatePackageDTO getRatePackageById(Long id) {
        log.debug("Fetching rate package DTO with ID: {} from cache", id);
        return packageDtoCache.get(id);
    }

    public void addToCache(RatePackageDTO dto) {
        log.debug("Adding rate package ID: {} to cache", dto.getRatePackageId());
        packageDtoCache.put(dto.getRatePackageId(), dto);
    }

    public void updateCache(RatePackageDTO dto) {
        log.debug("Updating cache for rate package ID: {}", dto.getRatePackageId());
        packageDtoCache.put(dto.getRatePackageId(), dto);
    }

    public void removeFromCache(Long id) {
        log.debug("Removing rate package ID: {} from cache", id);
        packageDtoCache.remove(id);
    }

    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing rate package cache...");
        packageDtoCache.clear();
        preloadCache();
    }
}