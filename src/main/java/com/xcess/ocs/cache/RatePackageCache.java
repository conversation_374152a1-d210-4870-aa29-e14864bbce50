package com.xcess.ocs.cache;

import com.xcess.ocs.dto.RatePackageDTO;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.mapper.RatePackageMapper;
import com.xcess.ocs.ratingengine.cache.RatePackageTries;
import com.xcess.ocs.repository.RatePackageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class RatePackageCache {
//    private static final Logger log = LoggerFactory.getLogger(RatePackageCache.class);
    private final RatePackageRepository ratePackageRepository;

    // Cache to store all RatePackageDTOs by ID
    private final Map<Long, RatePackageDTO> packageDtoCache = new ConcurrentHashMap<>();
    // Cache to store RatePackageTries by RatePackage ID
    private final Map<Long, RatePackageTries> packageTriesCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void preloadCache() {
        log.info("Preloading rate package cache and associated tries from database...");
        List<RatePackage> packages = ratePackageRepository.findAll();
        for (RatePackage ratePackage : packages) {
            log.debug("Loading RatePackage ID: {} with {} RateDetails",
                    ratePackage.getRatePackageId(),
                    ratePackage.getRateDetails() != null ? ratePackage.getRateDetails().size() : 0);
            
            // Cache DTO
            RatePackageDTO dto = RatePackageMapper.toDTO(ratePackage); 
            packageDtoCache.put(ratePackage.getRatePackageId(), dto);

            // Create and cache Tries
            RatePackageTries tries = new RatePackageTries();
            if (ratePackage.getRateDetails() != null) {
                ratePackage.getRateDetails().forEach(rateDetail -> {
                    // Associate rateDetail with its parent package for created_at or other attributes if needed by tries
                    // For now, assuming RateDetails has a reference back to RatePackage or necessary fields directly
                    // or that RateCandidate creation will handle fetching RatePackage.createdAt if needed
                    if (rateDetail.getSourcePrefix() != null && rateDetail.getDestinationPrefix() != null) {
                        tries.getSourceDestinationTrie().insert(rateDetail);
                    } else if (rateDetail.getDestinationPrefix() != null) {
                        tries.getDestinationOnlyTrie().insert(rateDetail);
                    } else {
                        log.warn("Skipping RateDetail ID {} for RatePackage ID {}: Insufficient prefix info for trie insertion.", 
                                 rateDetail.getRateDetailsId(), ratePackage.getRatePackageId());
                    }
                });
            }
            packageTriesCache.put(ratePackage.getRatePackageId(), tries);
            log.debug("Built and cached tries for RatePackage ID: {}. Stats: {}", ratePackage.getRatePackageId(), tries.getStatistics());
        }
        log.info("Preloaded {} rate packages and their tries into cache.", packageDtoCache.size());
    }

    public List<RatePackageDTO> getAllRatePackages() {
        log.debug("Fetching all rate packages from DTO Cache");
        return new ArrayList<>(packageDtoCache.values());
    }

    public RatePackageDTO getRatePackageById(Long id) {
        log.debug("Fetching rate package DTO with ID: {} from Cache", id);
        return packageDtoCache.get(id);
    }

    public RatePackageTries getRatePackageTriesById(Long id) {
        log.debug("Fetching rate package tries for ID: {} from Cache", id);
        return packageTriesCache.get(id);
    }

    public Collection<RatePackageTries> getAllRatePackageTries() {
        log.debug("Fetching all rate package tries from Cache");
        return new ArrayList<>(packageTriesCache.values());
    }

    public void addToCache(RatePackageDTO dto, RatePackage originalEntity) {
        log.debug("Adding rate package ID: {} to Cache", dto.getRatePackageId());
        packageDtoCache.put(dto.getRatePackageId(), dto);
        
        RatePackageTries tries = new RatePackageTries();
        if (originalEntity != null && originalEntity.getRateDetails() != null) {
            originalEntity.getRateDetails().forEach(rateDetail -> {
                if (rateDetail.getSourcePrefix() != null && rateDetail.getDestinationPrefix() != null) {
                    tries.getSourceDestinationTrie().insert(rateDetail);
                } else if (rateDetail.getDestinationPrefix() != null) {
                    tries.getDestinationOnlyTrie().insert(rateDetail);
                }
            });
        }
        packageTriesCache.put(dto.getRatePackageId(), tries);
        log.debug("Added/Updated tries for RatePackage ID: {} in Cache", dto.getRatePackageId());
    }

    public void updateCache(RatePackageDTO dto, RatePackage originalEntity) {
        log.debug("Updating Cache for rate package ID: {}", dto.getRatePackageId());
        addToCache(dto, originalEntity);
    }

    public void removeFromCache(Long id) {
        log.debug("Removing rate package ID: {} from Cache", id);
        packageDtoCache.remove(id);
        RatePackageTries removedTries = packageTriesCache.remove(id);
        if (removedTries != null) {
            log.debug("Removed tries for RatePackage ID: {}", id);
        }
    }

    @Scheduled(fixedDelayString = "${cache.refresh.interval:600000}")
    public void refreshCache() {
        log.info("Refreshing rate package cache and tries...");
        packageDtoCache.clear();
        packageTriesCache.clear();
        preloadCache();
    }
}