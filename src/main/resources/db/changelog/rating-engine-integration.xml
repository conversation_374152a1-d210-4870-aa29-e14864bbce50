<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- Fix account ID column types to match entity (String instead of BIGINT) -->
    <changeSet id="fix-account-id-column-types" author="rating-engine-integration">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <tableExists tableName="rated_cdr"/>
        </preConditions>

        <comment>Fix account ID column types to store raw string values from Kafka</comment>

        <!-- Change incoming_account_id from BIGINT to VARCHAR -->
        <modifyDataType tableName="rated_cdr" columnName="incoming_account_id" newDataType="VARCHAR(50)"/>

        <!-- Change outgoing_account_id from BIGINT to VARCHAR -->
        <modifyDataType tableName="rated_cdr" columnName="outgoing_account_id" newDataType="VARCHAR(50)"/>

    </changeSet>

    <!-- Add rating engine fields to rated_cdr table -->
    <changeSet id="add-rating-engine-fields-to-rated-cdr" author="rating-engine-integration">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <tableExists tableName="rated_cdr"/>
            <not>
                <columnExists tableName="rated_cdr" columnName="applied_rate"/>
            </not>
        </preConditions>

        <comment>Add rating engine integration fields to rated_cdr table</comment>

        <!-- Applied rate per unit -->
        <addColumn tableName="rated_cdr">
            <column name="applied_rate" type="DECIMAL(10,4)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Rate package information -->
        <addColumn tableName="rated_cdr">
            <column name="rate_package_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="rated_cdr">
            <column name="rate_package_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Matched prefixes -->
        <addColumn tableName="rated_cdr">
            <column name="matched_source_prefix" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="rated_cdr">
            <column name="matched_destination_prefix" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Rating status and metadata -->
        <addColumn tableName="rated_cdr">
            <column name="rating_status" type="VARCHAR(20)" defaultValue="PENDING">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <addColumn tableName="rated_cdr">
            <column name="rating_failure_reason" type="TEXT">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="rated_cdr">
            <column name="rated_at" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Rate detail reference -->
        <addColumn tableName="rated_cdr">
            <column name="rate_detail_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Match type indicator -->
        <addColumn tableName="rated_cdr">
            <column name="is_source_destination_match" type="BOOLEAN">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Cost calculation fields -->
        <addColumn tableName="rated_cdr">
            <column name="total_cost" type="DECIMAL(10,4)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="rated_cdr">
            <column name="duration_minutes" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Add indexes for performance -->
        <createIndex tableName="rated_cdr" indexName="idx_rated_cdr_rating_status">
            <column name="rating_status"/>
        </createIndex>

        <createIndex tableName="rated_cdr" indexName="idx_rated_cdr_rate_package_id">
            <column name="rate_package_id"/>
        </createIndex>

        <createIndex tableName="rated_cdr" indexName="idx_rated_cdr_rate_detail_id">
            <column name="rate_detail_id"/>
        </createIndex>

        <createIndex tableName="rated_cdr" indexName="idx_rated_cdr_rated_at">
            <column name="rated_at"/>
        </createIndex>

        <!-- Add foreign key constraint to rate_details table -->
        <addForeignKeyConstraint
                baseTableName="rated_cdr"
                baseColumnNames="rate_detail_id"
                constraintName="fk_rated_cdr_rate_detail"
                referencedTableName="rate_details"
                referencedColumnNames="rate_details_id"
                onDelete="SET NULL"
                onUpdate="CASCADE"/>

    </changeSet>

    <!-- Update existing records to have PENDING status -->
    <changeSet id="update-existing-rated-cdr-status" author="rating-engine-integration">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <tableExists tableName="rated_cdr"/>
            <columnExists tableName="rated_cdr" columnName="rating_status"/>
        </preConditions>

        <comment>Update existing rated_cdr records to have PENDING rating status</comment>

        <update tableName="rated_cdr">
            <column name="rating_status" value="PENDING"/>
            <where>rating_status IS NULL</where>
        </update>

    </changeSet>

    <!-- Add check constraint for rating_status enum values -->
    <changeSet id="add-rating-status-check-constraint" author="rating-engine-integration">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <tableExists tableName="rated_cdr"/>
            <columnExists tableName="rated_cdr" columnName="rating_status"/>
        </preConditions>

        <comment>Add check constraint for rating_status enum values</comment>

        <sql>
            ALTER TABLE rated_cdr 
            ADD CONSTRAINT chk_rating_status 
            CHECK (rating_status IN ('RATED', 'UNRATED', 'FAILED', 'PENDING'))
        </sql>

        <rollback>
            ALTER TABLE rated_cdr DROP CONSTRAINT chk_rating_status
        </rollback>

    </changeSet>

</databaseChangeLog>
