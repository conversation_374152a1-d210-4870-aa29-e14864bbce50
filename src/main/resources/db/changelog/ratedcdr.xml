<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.10.xsd">

    <changeSet id="5" author="system">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <not>
                <tableExists tableName="rated_cdr"/>
            </not>
        </preConditions>

        <createTable tableName="rated_cdr">
            <column name="rated_cdr_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <!-- Core CDR fields -->
            <column name="calling_number" type="VARCHAR(50)" />
            <column name="called_number" type="VARCHAR(50)" />
            <column name="incoming_account_id" type="BIGINT" />
            <column name="outgoing_account_id" type="BIGINT" />
            <column name="start_time" type="TIMESTAMP" />
            <column name="end_time" type="TIMESTAMP" />
            <column name="source_id" type="BIGINT" />

        </createTable>
    </changeSet>
</databaseChangeLog>
