<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.10.xsd">

    <changeSet id="4" author="system">
        <createTable tableName="source_cdr_configuration">
            <column name="source_cdr_configuration_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="source_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="sequence" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Add foreign key constraint to source_id -->
        <addForeignKeyConstraint
                baseTableName="source_cdr_configuration"
                baseColumnNames="source_id"
                constraintName="fk_source_id"
                referencedTableName="source_configuration"
                referencedColumnNames="source_id"
                onDelete="CASCADE"/>
    </changeSet>

</databaseChangeLog>