spring.application.name=ocs
server.port=8060
server.address=0.0.0.0

spring.datasource.url=jdbc:mysql://*************:3306/xcessocs?allowPublicKeyRetrieval=true&useSSL=false&autoreconnect=true&createDatabaseIfNotExist=true
spring.datasource.username=ocs
spring.datasource.password=ocs@2025@db
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update

spring.jackson.mapper.ACCEPT_CASE_INSENSITIVE_ENUMS=true

spring.liquibase.change-log=classpath:/db/changelog/db.changelog-master.xml

# Content negotiation configuration
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.media-types.json=application/json
# Remove these deprecated properties
# spring.http.encoding.charset=UTF-8
# spring.http.encoding.enabled=true
# spring.http.encoding.force=tru
# Replace with these new properties
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

springdoc.swagger-ui.operations-sorter=alpha
springdoc.swagger-ui.tags-sorter=alpha
management.endpoints.web.exposure.include=*
management.endpoints.web.base-path=/actuator

# JWT Configuration
jwt.secret=your_strong_secret_key_here_minimum_32_characters_long_for_security
jwt.expiration.ms=86400000

logging.level.org.springframework.boot.actuate=DEBUG
logging.level.org.springframework.web.servlet.DispatcherServlet=DEBUG

cache.refresh.interval=300000

# Kafka Consumer Configuration
spring.kafka.bootstrap-servers=*************:29092
kafka.subscription.refresh.interval=300000
spring.kafka.consumer.group-id=ocs-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.listener.concurrency=3
spring.kafka.consumer.properties.spring.json.trusted.packages=*

logging.level.root=INFO
logging.level.com.xcess.ocs=INFO
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.org.hibernate.SQL=OFF

# Keep essential logs
logging.level.org.springframework.kafka=INFO

# Suppress overly verbose internal Kafka logs
logging.level.org.apache.kafka.clients.consumer=ERROR
logging.level.org.apache.kafka.clients.Metadata=ERROR
logging.level.org.apache.kafka.common.utils.AppInfoParser=ERROR
logging.level.org.apache.kafka.common.metrics=ERROR
logging.level.org.apache.kafka.clients.FetchSessionHandler=ERROR
logging.level.org.apache.kafka.clients.NetworkClient=ERROR
logging.level.org.apache.kafka.clients.consumer.internals.ConsumerCoordinator=ERROR
logging.level.org.apache.kafka.clients.consumer.internals.SubscriptionState=ERROR