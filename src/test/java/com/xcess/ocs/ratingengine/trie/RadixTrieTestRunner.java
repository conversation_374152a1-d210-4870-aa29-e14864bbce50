package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.ratingengine.model.RateCandidate;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Simple test runner to verify radix trie functionality without JUnit dependencies
 * 
 * <AUTHOR> Developer
 */
public class RadixTrieTestRunner {

    public static void main(String[] args) {
        System.out.println("Starting Radix Trie Tests...");
        
        RadixTrieTestRunner runner = new RadixTrieTestRunner();
        
        try {
            runner.testRadixTrieNode();
            runner.testRadixDestinationOnlyTrie();
            runner.testRadixSourceDestinationTrie();
            
            System.out.println("\n[PASS] All tests passed successfully!");
        } catch (Exception e) {
            System.err.println("\n[FAIL] Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void testRadixTrieNode() {
        System.out.println("\n[TEST] Testing RadixTrieNode...");
        
        // Test basic functionality
        RadixTrieNode node = new RadixTrieNode();
        assert node.getEdgeLabel().equals("") : "Default edge label should be empty";
        assert !node.isEndOfPrefix() : "Should not be end of prefix initially";
        assert !node.hasChildren() : "Should not have children initially";
        assert !node.hasRateDetails() : "Should not have rate details initially";
        
        // Test edge label
        node.setEdgeLabel("test");
        assert node.getEdgeLabel().equals("test") : "Edge label should be 'test'";
        
        // Test children
        RadixTrieNode child = new RadixTrieNode("child");
        node.addChild('c', child);
        assert node.hasChildren() : "Should have children";
        assert node.getChild('c') == child : "Should return correct child";
        
        // Test rate details
        RatePackage ratePackage = RatePackage.builder()
                .ratePackageId(1L)
                .packageName("Test Package")
                .ratePackageType(RatePackageType.DESTINATION_BASED)
                .build();
        
        RateDetails rateDetail = RateDetails.builder()
                .rateDetailsId(1L)
                .destinationPrefix("91")
                .rate(0.05)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(ratePackage)
                .build();
        
        node.addRateDetail(rateDetail);
        assert node.hasRateDetails() : "Should have rate details";
        assert node.getRateDetailsCount() == 1 : "Should have 1 rate detail";
        
        // Test common prefix finding
        assert RadixTrieNode.findCommonPrefixLength("abc", "abx") == 2 : "Common prefix should be 2";
        assert RadixTrieNode.findCommonPrefixLength("abc", "def") == 0 : "Common prefix should be 0";
        assert RadixTrieNode.findCommonPrefixLength("abc", "abc") == 3 : "Common prefix should be 3";
        
        System.out.println("[PASS] RadixTrieNode tests passed");
    }
    
    private void testRadixDestinationOnlyTrie() {
        System.out.println("\n[TEST] Testing RadixDestinationOnlyTrie...");
        
        RadixDestinationOnlyTrie trie = new RadixDestinationOnlyTrie();
        
        // Create test rate details
        RatePackage ratePackage = RatePackage.builder()
                .ratePackageId(1L)
                .packageName("Test Package")
                .ratePackageType(RatePackageType.DESTINATION_BASED)
                .build();
        
        RateDetails rateDetail1 = RateDetails.builder()
                .rateDetailsId(1L)
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(ratePackage)
                .build();
        
        RateDetails rateDetail2 = RateDetails.builder()
                .rateDetailsId(2L)
                .destinationPrefix("919")
                .destinationPrefixName("India Mobile")
                .rate(0.04)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(ratePackage)
                .build();
        
        // Test insertion
        trie.insert(rateDetail1);
        trie.insert(rateDetail2);
        
        assert trie.getTotalRateDetailsCount() == 2 : "Should have 2 rate details";
        
        // Test search
        List<RateCandidate> candidates = trie.search("************");
        assert candidates.size() == 2 : "Should find 2 candidates for ************";
        
        boolean found91 = candidates.stream().anyMatch(c -> "91".equals(c.getMatchedDestinationPrefix()));
        boolean found919 = candidates.stream().anyMatch(c -> "919".equals(c.getMatchedDestinationPrefix()));
        
        assert found91 : "Should find 91 prefix";
        assert found919 : "Should find 919 prefix";
        
        // Test longest prefix match
        List<RateCandidate> lpmCandidates = trie.longestPrefixMatch("************");
        assert lpmCandidates.size() == 2 : "LPM should find 2 candidates";
        
        // Test clear
        trie.clear();
        assert trie.getTotalRateDetailsCount() == 0 : "Should have 0 rate details after clear";
        
        System.out.println("[PASS] RadixDestinationOnlyTrie tests passed");
    }
    
    private void testRadixSourceDestinationTrie() {
        System.out.println("\n[TEST] Testing RadixSourceDestinationTrie...");
        
        RadixSourceDestinationTrie trie = new RadixSourceDestinationTrie();
        
        // Create test rate details
        RatePackage ratePackage = RatePackage.builder()
                .ratePackageId(1L)
                .packageName("Test Package")
                .ratePackageType(RatePackageType.SOURCE_DESTINATION_BASED)
                .build();
        
        RateDetails rateDetail1 = RateDetails.builder()
                .rateDetailsId(1L)
                .sourcePrefix("1")
                .sourcePrefixName("US")
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(ratePackage)
                .build();
        
        RateDetails rateDetail2 = RateDetails.builder()
                .rateDetailsId(2L)
                .sourcePrefix("12")
                .sourcePrefixName("US-NY")
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.04)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(ratePackage)
                .build();
        
        // Test insertion
        trie.insert(rateDetail1);
        trie.insert(rateDetail2);
        
        assert trie.getTotalRateDetailsCount() == 2 : "Should have 2 rate details";
        
        // Test search
        List<RateCandidate> candidates = trie.search("12125551234", "************");
        assert candidates.size() == 2 : "Should find 2 candidates";
        
        boolean found1_91 = candidates.stream().anyMatch(c -> 
            "1".equals(c.getMatchedSourcePrefix()) && "91".equals(c.getMatchedDestinationPrefix()));
        boolean found12_91 = candidates.stream().anyMatch(c -> 
            "12".equals(c.getMatchedSourcePrefix()) && "91".equals(c.getMatchedDestinationPrefix()));
        
        assert found1_91 : "Should find 1->91 route";
        assert found12_91 : "Should find 12->91 route";
        
        // Test clear
        trie.clear();
        assert trie.getTotalRateDetailsCount() == 0 : "Should have 0 rate details after clear";
        
        System.out.println("[PASS] RadixSourceDestinationTrie tests passed");
    }
}
