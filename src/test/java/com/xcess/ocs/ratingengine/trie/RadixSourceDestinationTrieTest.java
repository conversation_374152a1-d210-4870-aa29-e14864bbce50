package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RadixSourceDestinationTrie
 * 
 * <AUTHOR> Developer
 */
class RadixSourceDestinationTrieTest {

    private RadixSourceDestinationTrie radixTrie;
    private RatePackage testRatePackage;

    @BeforeEach
    void setUp() {
        radixTrie = new RadixSourceDestinationTrie();
        testRatePackage = new RatePackage();
        testRatePackage.setRatePackageId(1L);
        testRatePackage.setPackageName("Test Package");
    }

    private RateDetails createRateDetail(String sourcePrefix, String destinationPrefix, double rate) {
        return RateDetails.builder()
                .rateDetailsId((long) (Math.random() * 1000))
                .sourcePrefix(sourcePrefix)
                .sourcePrefixName("Test Source")
                .destinationPrefix(destinationPrefix)
                .destinationPrefixName("Test Destination")
                .rate(rate)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(testRatePackage)
                .build();
    }

    @Test
    void testInsertAndSearchSingleRoute() {
        // Given
        RateDetails rateDetail = createRateDetail("1", "91", 0.05);
        
        // When
        radixTrie.insert(rateDetail);
        List<RateCandidate> candidates = radixTrie.search("***********", "************");
        
        // Then
        assertFalse(candidates.isEmpty());
        assertEquals(1, candidates.size());
        assertEquals("1", candidates.get(0).getMatchedSourcePrefix());
        assertEquals("91", candidates.get(0).getMatchedDestinationPrefix());
        assertTrue(candidates.get(0).isSourceDestinationMatch());
        assertEquals(rateDetail.getRate(), candidates.get(0).getRateDetail().getRate());
    }

    @Test
    void testInsertAndSearchMultipleRoutes() {
        // Given
        RateDetails rateDetail1 = createRateDetail("1", "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("12", "91", 0.04);
        RateDetails rateDetail3 = createRateDetail("1", "919", 0.03);
        RateDetails rateDetail4 = createRateDetail("12", "919", 0.02);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        radixTrie.insert(rateDetail3);
        radixTrie.insert(rateDetail4);
        
        List<RateCandidate> candidates = radixTrie.search("***********", "************");
        
        // Then
        assertEquals(4, candidates.size());
        
        // Verify all combinations are found
        boolean found1_91 = candidates.stream().anyMatch(c -> 
            "1".equals(c.getMatchedSourcePrefix()) && "91".equals(c.getMatchedDestinationPrefix()));
        boolean found12_91 = candidates.stream().anyMatch(c -> 
            "12".equals(c.getMatchedSourcePrefix()) && "91".equals(c.getMatchedDestinationPrefix()));
        boolean found1_919 = candidates.stream().anyMatch(c -> 
            "1".equals(c.getMatchedSourcePrefix()) && "919".equals(c.getMatchedDestinationPrefix()));
        boolean found12_919 = candidates.stream().anyMatch(c -> 
            "12".equals(c.getMatchedSourcePrefix()) && "919".equals(c.getMatchedDestinationPrefix()));
        
        assertTrue(found1_91);
        assertTrue(found12_91);
        assertTrue(found1_919);
        assertTrue(found12_919);
    }

    @Test
    void testRadixCompressionWithLongPrefixes() {
        // Given - Insert routes with long prefixes that should be compressed
        RateDetails rateDetail1 = createRateDetail("123456789", "987654321", 0.05);
        RateDetails rateDetail2 = createRateDetail("123456780", "987654320", 0.06);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then - Verify both can be found
        List<RateCandidate> candidates1 = radixTrie.search("123456789123", "987654321456");
        List<RateCandidate> candidates2 = radixTrie.search("123456780456", "987654320789");
        
        assertEquals(1, candidates1.size());
        assertEquals("123456789", candidates1.get(0).getMatchedSourcePrefix());
        assertEquals("987654321", candidates1.get(0).getMatchedDestinationPrefix());
        
        assertEquals(1, candidates2.size());
        assertEquals("123456780", candidates2.get(0).getMatchedSourcePrefix());
        assertEquals("987654320", candidates2.get(0).getMatchedDestinationPrefix());
    }

    @Test
    void testSearchWithNoMatch() {
        // Given
        RateDetails rateDetail = createRateDetail("1", "91", 0.05);
        radixTrie.insert(rateDetail);
        
        // When - Search with non-matching source
        List<RateCandidate> candidates1 = radixTrie.search("44123456789", "************");
        
        // When - Search with non-matching destination
        List<RateCandidate> candidates2 = radixTrie.search("***********", "44123456789");
        
        // Then
        assertTrue(candidates1.isEmpty());
        assertTrue(candidates2.isEmpty());
    }

    @Test
    void testInsertWithNullPrefixes() {
        // Given
        RateDetails rateDetail1 = createRateDetail(null, "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("1", null, 0.05);
        RateDetails rateDetail3 = createRateDetail(null, null, 0.05);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        radixTrie.insert(rateDetail3);
        
        // Then - Should not crash, and trie should remain empty
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testClear() {
        // Given
        RateDetails rateDetail1 = createRateDetail("1", "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", "1", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // When
        radixTrie.clear();
        
        // Then
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
        assertTrue(radixTrie.search("***********", "************").isEmpty());
        assertTrue(radixTrie.search("441234567890", "***********").isEmpty());
    }

    @Test
    void testGetStatistics() {
        // Given
        RateDetails rateDetail1 = createRateDetail("1", "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", "1", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // When
        String stats = radixTrie.getStatistics();
        
        // Then
        assertNotNull(stats);
        assertTrue(stats.contains("RadixSourceDestinationTrie Stats"));
        assertTrue(stats.contains("nodes"));
        assertTrue(stats.contains("rate details"));
    }

    @Test
    void testGetTotalRateDetailsCount() {
        // Given
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
        
        // When
        RateDetails rateDetail1 = createRateDetail("1", "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", "1", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then
        assertEquals(2, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testMultipleRateDetailsForSameRoute() {
        // Given
        RateDetails rateDetail1 = createRateDetail("1", "91", 0.05);
        RateDetails rateDetail2 = createRateDetail("1", "91", 0.04); // Different rate for same route
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then
        List<RateCandidate> candidates = radixTrie.search("***********", "************");
        assertEquals(2, candidates.size());
        assertEquals(2, radixTrie.getTotalRateDetailsCount());
        
        // Both should have same prefixes but different rates
        assertTrue(candidates.stream().allMatch(c -> 
            "1".equals(c.getMatchedSourcePrefix()) && "91".equals(c.getMatchedDestinationPrefix())));
        assertTrue(candidates.stream().anyMatch(c -> c.getRateDetail().getRate().equals(0.05)));
        assertTrue(candidates.stream().anyMatch(c -> c.getRateDetail().getRate().equals(0.04)));
    }

    @Test
    void testEdgeCaseEmptySearch() {
        // Given
        RateDetails rateDetail = createRateDetail("1", "91", 0.05);
        radixTrie.insert(rateDetail);
        
        // When & Then
        assertTrue(radixTrie.search("", "************").isEmpty());
        assertTrue(radixTrie.search("***********", "").isEmpty());
        assertTrue(radixTrie.search(null, "************").isEmpty());
        assertTrue(radixTrie.search("***********", null).isEmpty());
    }

    @Test
    void testComplexRoutingScenario() {
        // Given - Complex routing scenario with multiple overlapping routes
        RateDetails[] rateDetails = {
            createRateDetail("1", "91", 0.10),      // US to India
            createRateDetail("12", "91", 0.08),     // US-NY to India
            createRateDetail("121", "91", 0.06),    // US-NY-NYC to India
            createRateDetail("1", "919", 0.09),     // US to India-Mobile
            createRateDetail("12", "919", 0.07),    // US-NY to India-Mobile
            createRateDetail("121", "919", 0.05),   // US-NY-NYC to India-Mobile
            createRateDetail("44", "1", 0.12),      // UK to US
            createRateDetail("442", "1", 0.10),     // UK-London to US
            createRateDetail("44", "12", 0.11),     // UK to US-NY
            createRateDetail("442", "12", 0.09)     // UK-London to US-NY
        };
        
        // When
        for (RateDetails rateDetail : rateDetails) {
            radixTrie.insert(rateDetail);
        }
        
        // Then - Test various search scenarios
        
        // US-NY-NYC to India-Mobile should find 6 matches
        List<RateCandidate> candidates1 = radixTrie.search("***********", "************");
        assertEquals(6, candidates1.size());
        
        // UK-London to US-NY should find 4 matches
        List<RateCandidate> candidates2 = radixTrie.search("************", "***********");
        assertEquals(4, candidates2.size());
        
        // Verify total count
        assertEquals(rateDetails.length, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testRadixTrieMemoryEfficiency() {
        // Given - Insert many routes with common prefixes to test compression
        String[] sourcePrefixes = {"123456789", "123456780", "987654321"};
        String[] destPrefixes = {"111222333", "111222334", "555666777"};
        
        // When
        for (String sourcePrefix : sourcePrefixes) {
            for (String destPrefix : destPrefixes) {
                RateDetails rateDetail = createRateDetail(sourcePrefix, destPrefix, 0.05);
                radixTrie.insert(rateDetail);
            }
        }
        
        // Then - All should be searchable
        for (String sourcePrefix : sourcePrefixes) {
            for (String destPrefix : destPrefixes) {
                List<RateCandidate> candidates = radixTrie.search(sourcePrefix + "999", destPrefix + "888");
                assertEquals(1, candidates.size());
                assertEquals(sourcePrefix, candidates.get(0).getMatchedSourcePrefix());
                assertEquals(destPrefix, candidates.get(0).getMatchedDestinationPrefix());
            }
        }
        
        assertEquals(sourcePrefixes.length * destPrefixes.length, radixTrie.getTotalRateDetailsCount());
    }
}
