package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.ratingengine.model.RateCandidate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RadixDestinationOnlyTrie
 * 
 * <AUTHOR> Developer
 */
class RadixDestinationOnlyTrieTest {

    private RadixDestinationOnlyTrie radixTrie;
    private RatePackage testRatePackage;

    @BeforeEach
    void setUp() {
        radixTrie = new RadixDestinationOnlyTrie();
        testRatePackage = new RatePackage();
        testRatePackage.setRatePackageId(1L);
        testRatePackage.setPackageName("Test Package");
    }

    private RateDetails createRateDetail(String destinationPrefix, double rate) {
        return RateDetails.builder()
                .rateDetailsId((long) (Math.random() * 1000))
                .destinationPrefix(destinationPrefix)
                .destinationPrefixName("Test Destination")
                .rate(rate)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(testRatePackage)
                .build();
    }

    @Test
    void testInsertAndSearchSinglePrefix() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        
        // When
        radixTrie.insert(rateDetail);
        List<RateCandidate> candidates = radixTrie.search("919876543210");
        
        // Then
        assertFalse(candidates.isEmpty());
        assertEquals(1, candidates.size());
        assertEquals("91", candidates.get(0).getMatchedDestinationPrefix());
        assertEquals(rateDetail.getRate(), candidates.get(0).getRateDetail().getRate());
    }

    @Test
    void testInsertAndSearchMultiplePrefixes() {
        // Given
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("919", 0.04);
        RateDetails rateDetail3 = createRateDetail("9198", 0.03);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        radixTrie.insert(rateDetail3);
        
        List<RateCandidate> candidates = radixTrie.search("919876543210");
        
        // Then
        assertEquals(3, candidates.size());
        
        // Verify all prefixes are found
        boolean found91 = candidates.stream().anyMatch(c -> "91".equals(c.getMatchedDestinationPrefix()));
        boolean found919 = candidates.stream().anyMatch(c -> "919".equals(c.getMatchedDestinationPrefix()));
        boolean found9198 = candidates.stream().anyMatch(c -> "9198".equals(c.getMatchedDestinationPrefix()));
        
        assertTrue(found91);
        assertTrue(found919);
        assertTrue(found9198);
    }

    @Test
    void testLongestPrefixMatch() {
        // Given
        RateDetails rateDetail1 = createRateDetail("1", 0.10);
        RateDetails rateDetail2 = createRateDetail("12", 0.08);
        RateDetails rateDetail3 = createRateDetail("121", 0.06);
        RateDetails rateDetail4 = createRateDetail("1212", 0.04);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        radixTrie.insert(rateDetail3);
        radixTrie.insert(rateDetail4);
        
        List<RateCandidate> candidates = radixTrie.longestPrefixMatch("12125551234");
        
        // Then
        assertEquals(4, candidates.size());
        
        // Verify all matching prefixes are found
        boolean found1 = candidates.stream().anyMatch(c -> "1".equals(c.getMatchedDestinationPrefix()));
        boolean found12 = candidates.stream().anyMatch(c -> "12".equals(c.getMatchedDestinationPrefix()));
        boolean found121 = candidates.stream().anyMatch(c -> "121".equals(c.getMatchedDestinationPrefix()));
        boolean found1212 = candidates.stream().anyMatch(c -> "1212".equals(c.getMatchedDestinationPrefix()));
        
        assertTrue(found1);
        assertTrue(found12);
        assertTrue(found121);
        assertTrue(found1212);
    }

    @Test
    void testRadixCompressionWithLongPrefixes() {
        // Given - Insert prefixes that should be compressed in radix trie
        RateDetails rateDetail1 = createRateDetail("123456789", 0.05);
        RateDetails rateDetail2 = createRateDetail("123456780", 0.06);
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then - Verify both can be found
        List<RateCandidate> candidates1 = radixTrie.search("123456789123");
        List<RateCandidate> candidates2 = radixTrie.search("123456780456");
        
        assertEquals(1, candidates1.size());
        assertEquals("123456789", candidates1.get(0).getMatchedDestinationPrefix());
        
        assertEquals(1, candidates2.size());
        assertEquals("123456780", candidates2.get(0).getMatchedDestinationPrefix());
    }

    @Test
    void testSearchWithNoMatch() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        radixTrie.insert(rateDetail);
        
        // When
        List<RateCandidate> candidates = radixTrie.search("44123456789");
        
        // Then
        assertTrue(candidates.isEmpty());
    }

    @Test
    void testInsertWithNullDestinationPrefix() {
        // Given
        RateDetails rateDetail = createRateDetail(null, 0.05);
        
        // When
        radixTrie.insert(rateDetail);
        
        // Then - Should not crash, and trie should remain empty
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testInsertWithSourcePrefix() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        rateDetail.setSourcePrefix("1"); // This should be rejected
        
        // When
        radixTrie.insert(rateDetail);
        
        // Then - Should not be inserted
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testClear() {
        // Given
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // When
        radixTrie.clear();
        
        // Then
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
        assertTrue(radixTrie.search("919876543210").isEmpty());
        assertTrue(radixTrie.search("441234567890").isEmpty());
    }

    @Test
    void testGetStatistics() {
        // Given
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // When
        String stats = radixTrie.getStatistics();
        
        // Then
        assertNotNull(stats);
        assertTrue(stats.contains("RadixDestinationOnlyTrie Stats"));
        assertTrue(stats.contains("nodes"));
        assertTrue(stats.contains("rate details"));
    }

    @Test
    void testGetTotalRateDetailsCount() {
        // Given
        assertEquals(0, radixTrie.getTotalRateDetailsCount());
        
        // When
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("44", 0.06);
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then
        assertEquals(2, radixTrie.getTotalRateDetailsCount());
    }

    @Test
    void testMultipleRateDetailsForSamePrefix() {
        // Given
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("91", 0.04); // Different rate for same prefix
        
        // When
        radixTrie.insert(rateDetail1);
        radixTrie.insert(rateDetail2);
        
        // Then
        List<RateCandidate> candidates = radixTrie.search("919876543210");
        assertEquals(2, candidates.size());
        assertEquals(2, radixTrie.getTotalRateDetailsCount());
        
        // Both should have same destination prefix but different rates
        assertTrue(candidates.stream().allMatch(c -> "91".equals(c.getMatchedDestinationPrefix())));
        assertTrue(candidates.stream().anyMatch(c -> c.getRateDetail().getRate().equals(0.05)));
        assertTrue(candidates.stream().anyMatch(c -> c.getRateDetail().getRate().equals(0.04)));
    }

    @Test
    void testEdgeCaseEmptySearch() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        radixTrie.insert(rateDetail);
        
        // When & Then
        assertTrue(radixTrie.search("").isEmpty());
        assertTrue(radixTrie.search(null).isEmpty());
        assertTrue(radixTrie.longestPrefixMatch("").isEmpty());
        assertTrue(radixTrie.longestPrefixMatch(null).isEmpty());
    }

    @Test
    void testRadixTrieMemoryEfficiency() {
        // Given - Insert many prefixes with common prefixes to test compression
        String[] prefixes = {
            "123456789001", "123456789002", "123456789003", "123456789004", "123456789005",
            "123456780001", "123456780002", "123456780003",
            "987654321001", "987654321002"
        };
        
        // When
        for (String prefix : prefixes) {
            RateDetails rateDetail = createRateDetail(prefix, 0.05);
            radixTrie.insert(rateDetail);
        }
        
        // Then - All should be searchable
        for (String prefix : prefixes) {
            List<RateCandidate> candidates = radixTrie.search(prefix + "999");
            assertEquals(1, candidates.size());
            assertEquals(prefix, candidates.get(0).getMatchedDestinationPrefix());
        }
        
        assertEquals(prefixes.length, radixTrie.getTotalRateDetailsCount());
    }
}
