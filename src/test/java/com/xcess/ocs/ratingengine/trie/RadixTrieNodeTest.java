package com.xcess.ocs.ratingengine.trie;

import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RadixTrieNode
 * 
 * <AUTHOR> Developer
 */
class RadixTrieNodeTest {

    private RadixTrieNode node;
    private RatePackage testRatePackage;

    @BeforeEach
    void setUp() {
        node = new RadixTrieNode();
        testRatePackage = new RatePackage();
        testRatePackage.setRatePackageId(1L);
        testRatePackage.setPackageName("Test Package");
    }

    private RateDetails createRateDetail(String prefix, double rate) {
        return RateDetails.builder()
                .rateDetailsId((long) (Math.random() * 1000))
                .destinationPrefix(prefix)
                .destinationPrefixName("Test Destination")
                .rate(rate)
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now().plusDays(1))
                .currentVersion(1)
                .ratePackage(testRatePackage)
                .build();
    }

    @Test
    void testDefaultConstructor() {
        // Given & When
        RadixTrieNode newNode = new RadixTrieNode();
        
        // Then
        assertNotNull(newNode.getChildren());
        assertTrue(newNode.getChildren().isEmpty());
        assertNotNull(newNode.getRateDetailsUnmodifiable());
        assertTrue(newNode.getRateDetailsUnmodifiable().isEmpty());
        assertFalse(newNode.isEndOfPrefix());
        assertEquals("", newNode.getEdgeLabel());
        assertNull(newNode.getPrefix());
    }

    @Test
    void testConstructorWithEdgeLabel() {
        // Given & When
        RadixTrieNode newNode = new RadixTrieNode("test");
        
        // Then
        assertEquals("test", newNode.getEdgeLabel());
        assertFalse(newNode.isEndOfPrefix());
        assertNull(newNode.getPrefix());
    }

    @Test
    void testConstructorWithEdgeLabelAndPrefix() {
        // Given & When
        RadixTrieNode newNode = new RadixTrieNode("edge", "fullPrefix");
        
        // Then
        assertEquals("edge", newNode.getEdgeLabel());
        assertEquals("fullPrefix", newNode.getPrefix());
        assertFalse(newNode.isEndOfPrefix());
    }

    @Test
    void testAddRateDetail() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        
        // When
        node.addRateDetail(rateDetail);
        
        // Then
        assertTrue(node.hasRateDetails());
        assertEquals(1, node.getRateDetailsCount());
        assertEquals(rateDetail, node.getRateDetailsUnmodifiable().get(0));
    }

    @Test
    void testAddMultipleRateDetails() {
        // Given
        RateDetails rateDetail1 = createRateDetail("91", 0.05);
        RateDetails rateDetail2 = createRateDetail("91", 0.04);
        List<RateDetails> rateDetailsList = Arrays.asList(rateDetail1, rateDetail2);
        
        // When
        node.addRateDetails(rateDetailsList);
        
        // Then
        assertTrue(node.hasRateDetails());
        assertEquals(2, node.getRateDetailsCount());
        assertTrue(node.getRateDetailsUnmodifiable().contains(rateDetail1));
        assertTrue(node.getRateDetailsUnmodifiable().contains(rateDetail2));
    }

    @Test
    void testAddNullRateDetail() {
        // When
        node.addRateDetail(null);
        
        // Then
        assertFalse(node.hasRateDetails());
        assertEquals(0, node.getRateDetailsCount());
    }

    @Test
    void testAddChild() {
        // Given
        RadixTrieNode child = new RadixTrieNode("child");
        
        // When
        node.addChild('c', child);
        
        // Then
        assertTrue(node.hasChildren());
        assertEquals(child, node.getChild('c'));
        assertTrue(node.getChildrenKeys().contains('c'));
    }

    @Test
    void testRemoveChild() {
        // Given
        RadixTrieNode child = new RadixTrieNode("child");
        node.addChild('c', child);
        
        // When
        RadixTrieNode removed = node.removeChild('c');
        
        // Then
        assertEquals(child, removed);
        assertFalse(node.hasChildren());
        assertNull(node.getChild('c'));
    }

    @Test
    void testMarkAsEndOfPrefix() {
        // Given
        String prefix = "testPrefix";
        
        // When
        node.markAsEndOfPrefix(prefix);
        
        // Then
        assertTrue(node.isEndOfPrefix());
        assertEquals(prefix, node.getPrefix());
    }

    @Test
    void testClearRateDetails() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        node.addRateDetail(rateDetail);
        
        // When
        node.clearRateDetails();
        
        // Then
        assertFalse(node.hasRateDetails());
        assertEquals(0, node.getRateDetailsCount());
        assertTrue(node.getRateDetailsUnmodifiable().isEmpty());
    }

    @Test
    void testCanBeRemoved() {
        // Given - Empty node
        // When & Then
        assertTrue(node.canBeRemoved());
        
        // Given - Node with rate details
        RateDetails rateDetail = createRateDetail("91", 0.05);
        node.addRateDetail(rateDetail);
        // When & Then
        assertFalse(node.canBeRemoved());
        
        // Given - Node with children
        node.clearRateDetails();
        RadixTrieNode child = new RadixTrieNode("child");
        node.addChild('c', child);
        // When & Then
        assertFalse(node.canBeRemoved());
        
        // Given - End of prefix node
        node.getChildren().clear();
        node.markAsEndOfPrefix("test");
        // When & Then
        assertFalse(node.canBeRemoved());
    }

    @Test
    void testFindCommonPrefixLength() {
        // Test cases for common prefix finding
        assertEquals(0, RadixTrieNode.findCommonPrefixLength(null, "test"));
        assertEquals(0, RadixTrieNode.findCommonPrefixLength("test", null));
        assertEquals(0, RadixTrieNode.findCommonPrefixLength("", "test"));
        assertEquals(0, RadixTrieNode.findCommonPrefixLength("test", ""));
        assertEquals(0, RadixTrieNode.findCommonPrefixLength("abc", "def"));
        assertEquals(1, RadixTrieNode.findCommonPrefixLength("abc", "axyz"));
        assertEquals(2, RadixTrieNode.findCommonPrefixLength("abc", "abxyz"));
        assertEquals(3, RadixTrieNode.findCommonPrefixLength("abc", "abc"));
        assertEquals(3, RadixTrieNode.findCommonPrefixLength("abc", "abcdef"));
        assertEquals(3, RadixTrieNode.findCommonPrefixLength("abcdef", "abc"));
        assertEquals(6, RadixTrieNode.findCommonPrefixLength("abcdef", "abcdef"));
    }

    @Test
    void testNeedsSplitting() {
        // Given
        node.setEdgeLabel("abcdef");
        
        // Test cases
        assertFalse(node.needsSplitting("", 0)); // Empty search string
        assertFalse(node.needsSplitting("xyz", 3)); // Start index beyond search string
        assertFalse(node.needsSplitting("xyz", 0)); // No common prefix
        assertFalse(node.needsSplitting("abcdef", 0)); // Full match
        assertFalse(node.needsSplitting("abcdefghi", 0)); // Search string longer, full edge match
        assertTrue(node.needsSplitting("abc", 0)); // Partial match, search string shorter
        assertTrue(node.needsSplitting("abcxyz", 0)); // Partial match, different suffix
    }

    @Test
    void testNeedsSplittingWithEmptyEdgeLabel() {
        // Given
        node.setEdgeLabel("");
        
        // When & Then
        assertFalse(node.needsSplitting("test", 0));
        
        // Given
        node.setEdgeLabel(null);
        
        // When & Then
        assertFalse(node.needsSplitting("test", 0));
    }

    @Test
    void testGetRateDetailsUnmodifiable() {
        // Given
        RateDetails rateDetail = createRateDetail("91", 0.05);
        node.addRateDetail(rateDetail);
        
        // When
        List<RateDetails> unmodifiableList = node.getRateDetailsUnmodifiable();
        
        // Then
        assertEquals(1, unmodifiableList.size());
        assertEquals(rateDetail, unmodifiableList.get(0));
        
        // Verify it's truly unmodifiable
        assertThrows(UnsupportedOperationException.class, () -> {
            unmodifiableList.add(createRateDetail("44", 0.06));
        });
    }

    @Test
    void testToString() {
        // Given
        node.setEdgeLabel("test");
        node.setPrefix("fullPrefix");
        node.markAsEndOfPrefix("fullPrefix");
        node.addRateDetail(createRateDetail("91", 0.05));
        node.addChild('c', new RadixTrieNode("child"));
        
        // When
        String result = node.toString();
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("RadixTrieNode"));
        assertTrue(result.contains("edgeLabel='test'"));
        assertTrue(result.contains("prefix='fullPrefix'"));
        assertTrue(result.contains("isEndOfPrefix=true"));
        assertTrue(result.contains("rateDetailsCount=1"));
        assertTrue(result.contains("childrenCount=1"));
    }

    @Test
    void testEdgeLabelHandling() {
        // Test edge label with null
        RadixTrieNode nodeWithNull = new RadixTrieNode(null);
        assertEquals("", nodeWithNull.getEdgeLabel());
        
        // Test setting edge label
        node.setEdgeLabel("newLabel");
        assertEquals("newLabel", node.getEdgeLabel());
        
        // Test setting null edge label
        node.setEdgeLabel(null);
        assertNull(node.getEdgeLabel());
    }

    @Test
    void testChildrenOperations() {
        // Test multiple children
        RadixTrieNode child1 = new RadixTrieNode("child1");
        RadixTrieNode child2 = new RadixTrieNode("child2");
        RadixTrieNode child3 = new RadixTrieNode("child3");
        
        node.addChild('a', child1);
        node.addChild('b', child2);
        node.addChild('c', child3);
        
        assertEquals(3, node.getChildren().size());
        assertEquals(3, node.getChildrenKeys().size());
        assertTrue(node.getChildrenKeys().contains('a'));
        assertTrue(node.getChildrenKeys().contains('b'));
        assertTrue(node.getChildrenKeys().contains('c'));
        
        // Test removing non-existent child
        assertNull(node.removeChild('z'));
        assertEquals(3, node.getChildren().size());
    }
}
