package com.xcess.ocs.service;

import com.xcess.ocs.dto.RateDetailDTO;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.RatePackageType;
import com.xcess.ocs.repository.CountryRepository;
import com.xcess.ocs.repository.RateDetailsHistoryRepository;
import com.xcess.ocs.repository.RateDetailsRepository;
import com.xcess.ocs.repository.RatePackageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RateDetailsServicePackageTypeValidationTest {

    @Mock
    private RateDetailsRepository rateDetailsRepository;

    @Mock
    private RatePackageRepository ratePackageRepository;

    @Mock
    private CountryRepository countryRepository;

    @Mock
    private RateDetailsHistoryRepository rateDetailsHistoryRepository;

    @InjectMocks
    private RateDetailsService rateDetailsService;

    private RatePackage destinationBasedPackage;
    private RatePackage sourceDestinationBasedPackage;
    private RateDetailDTO validDestinationOnlyDto;
    private RateDetailDTO validSourceDestinationDto;

    @BeforeEach
    void setUp() {
        // Create DESTINATION_BASED package
        destinationBasedPackage = new RatePackage();
        destinationBasedPackage.setRatePackageId(1L);
        destinationBasedPackage.setRatePackageType(RatePackageType.DESTINATION_BASED);

        // Create SOURCE_DESTINATION_BASED package
        sourceDestinationBasedPackage = new RatePackage();
        sourceDestinationBasedPackage.setRatePackageId(2L);
        sourceDestinationBasedPackage.setRatePackageType(RatePackageType.SOURCE_DESTINATION_BASED);

        // Create valid destination-only DTO
        validDestinationOnlyDto = RateDetailDTO.builder()
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(30))
                .build();

        // Create valid source-destination DTO
        validSourceDestinationDto = RateDetailDTO.builder()
                .sourcePrefix("1")
                .sourcePrefixName("USA")
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(30))
                .build();
    }

    @Test
    void testDestinationBasedPackage_WithSourcePrefix_ShouldThrowException() {
        // Arrange
        when(ratePackageRepository.findById(1L)).thenReturn(Optional.of(destinationBasedPackage));
        when(countryRepository.findAll()).thenReturn(Collections.emptyList());

        RateDetailDTO invalidDto = RateDetailDTO.builder()
                .sourcePrefix("1")  // This should not be allowed for DESTINATION_BASED
                .sourcePrefixName("USA")
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(30))
                .build();

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            rateDetailsService.createRateDetail(1L, Collections.singletonList(invalidDto));
        });

        assertTrue(exception.getMessage().contains("Source prefix is not allowed for DESTINATION_BASED rate packages"));
    }

    @Test
    void testSourceDestinationBasedPackage_WithoutSourcePrefix_ShouldThrowException() {
        // Arrange
        when(ratePackageRepository.findById(2L)).thenReturn(Optional.of(sourceDestinationBasedPackage));
        when(countryRepository.findAll()).thenReturn(Collections.emptyList());

        RateDetailDTO invalidDto = RateDetailDTO.builder()
                .destinationPrefix("91")  // Missing source prefix for SOURCE_DESTINATION_BASED
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(30))
                .build();

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            rateDetailsService.createRateDetail(2L, Collections.singletonList(invalidDto));
        });

        assertTrue(exception.getMessage().contains("Source prefix is required for SOURCE_DESTINATION_BASED rate packages"));
    }

    @Test
    void testSourceDestinationBasedPackage_WithoutSourcePrefixName_ShouldThrowException() {
        // Arrange
        when(ratePackageRepository.findById(2L)).thenReturn(Optional.of(sourceDestinationBasedPackage));
        when(countryRepository.findAll()).thenReturn(Collections.emptyList());

        RateDetailDTO invalidDto = RateDetailDTO.builder()
                .sourcePrefix("1")  // Has source prefix but missing source prefix name
                .destinationPrefix("91")
                .destinationPrefixName("India")
                .rate(0.05)
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(30))
                .build();

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            rateDetailsService.createRateDetail(2L, Collections.singletonList(invalidDto));
        });

        assertTrue(exception.getMessage().contains("Source prefix name is required when source prefix is provided"));
    }
}
